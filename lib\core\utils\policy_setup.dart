import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:guest_posts/core/models/policy_model.dart';
import 'package:guest_posts/core/data/sample_policies.dart';

/// Utility class for setting up policy documents in Firestore
/// This should be run once to populate the policies collection
class PolicySetup {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Populate Firestore with sample policy documents
  static Future<void> setupPolicies() async {
    try {
      // Privacy Policy
      await _createPolicy(
        PolicyType.privacy,
        SamplePolicies.privacyPolicy,
      );

      // Terms of Service
      await _createPolicy(
        PolicyType.terms,
        SamplePolicies.termsOfService,
      );

      // Refund Policy
      await _createPolicy(
        PolicyType.refund,
        SamplePolicies.refundPolicy,
      );

      // Publisher Terms
      await _createPolicy(
        PolicyType.publisherTerms,
        SamplePolicies.publisherTerms,
      );

      print('✅ All policies have been successfully created in Firestore');
    } catch (e) {
      print('❌ Error setting up policies: $e');
      rethrow;
    }
  }

  /// Create or update a single policy document
  static Future<void> _createPolicy(PolicyType policyType, String content) async {
    final policy = PolicyModel(
      id: policyType.id,
      content: content,
      updatedAt: DateTime.now(),
    );

    await _firestore
        .collection('policies')
        .doc(policyType.id)
        .set(policy.toFirestore());

    print('✅ Created policy: ${policyType.displayName}');
  }

  /// Update a specific policy content
  static Future<void> updatePolicy(PolicyType policyType, String newContent) async {
    try {
      final policy = PolicyModel(
        id: policyType.id,
        content: newContent,
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection('policies')
          .doc(policyType.id)
          .set(policy.toFirestore());

      print('✅ Updated policy: ${policyType.displayName}');
    } catch (e) {
      print('❌ Error updating policy ${policyType.displayName}: $e');
      rethrow;
    }
  }

  /// Check if policies exist in Firestore
  static Future<Map<PolicyType, bool>> checkPoliciesExist() async {
    final Map<PolicyType, bool> results = {};

    for (final policyType in PolicyType.values) {
      try {
        final doc = await _firestore
            .collection('policies')
            .doc(policyType.id)
            .get();
        results[policyType] = doc.exists;
      } catch (e) {
        results[policyType] = false;
      }
    }

    return results;
  }

  /// Get policy statistics
  static Future<Map<String, dynamic>> getPolicyStats() async {
    final stats = <String, dynamic>{};
    
    for (final policyType in PolicyType.values) {
      try {
        final doc = await _firestore
            .collection('policies')
            .doc(policyType.id)
            .get();
        
        if (doc.exists) {
          final data = doc.data()!;
          final updatedAt = (data['updatedAt'] as Timestamp?)?.toDate();
          stats[policyType.id] = {
            'exists': true,
            'updatedAt': updatedAt?.toIso8601String(),
            'contentLength': (data['content'] as String?)?.length ?? 0,
          };
        } else {
          stats[policyType.id] = {
            'exists': false,
            'updatedAt': null,
            'contentLength': 0,
          };
        }
      } catch (e) {
        stats[policyType.id] = {
          'exists': false,
          'error': e.toString(),
        };
      }
    }

    return stats;
  }

  /// Delete all policies (use with caution)
  static Future<void> deletePolicies() async {
    try {
      for (final policyType in PolicyType.values) {
        await _firestore
            .collection('policies')
            .doc(policyType.id)
            .delete();
        print('🗑️ Deleted policy: ${policyType.displayName}');
      }
      print('✅ All policies have been deleted');
    } catch (e) {
      print('❌ Error deleting policies: $e');
      rethrow;
    }
  }
}
