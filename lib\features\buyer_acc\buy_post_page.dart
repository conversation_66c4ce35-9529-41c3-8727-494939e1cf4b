import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:guest_posts/core/models/order_model.dart';
import 'package:guest_posts/core/utils/toast_helper.dart';
import 'package:guest_posts/core/utils/order_id_generator.dart';
import 'package:markdown_toolbar/markdown_toolbar.dart';
import 'package:guest_posts/core/theme/app_theme.dart';

class BuyPostPage extends StatefulWidget {
  final String websiteId;
  final Map<String, dynamic> websiteData;

  const BuyPostPage({
    super.key,
    required this.websiteId,
    required this.websiteData,
  });

  @override
  State<BuyPostPage> createState() => _BuyPostPageState();
}

class _BuyPostPageState extends State<BuyPostPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  final _linksController = TextEditingController();
  final _notesController = TextEditingController();
  bool _isLoading = false;
  bool _isPreviewMode = false;
  int _wordCount = 0;
  late final int _wordCountMin;
  late final int _wordCountMax;
  late final int _maxLinks;
  late final bool _isSponsored;
  late final double _basePrice;
  late final double _specialTopicsAdditionalPrice;
  late final bool _hasSpecialTopicsPricing;
  late String _backlinkType;
  late double _totalPrice;
  late final double? _dofollowCost;
  late final double? _spamScore;

  @override
  void initState() {
    super.initState();
    log(widget.websiteId);
    _wordCountMin = widget.websiteData['wordCountMin'] ?? 500;
    _wordCountMax = widget.websiteData['wordCountMax'] ?? 2000;
    _maxLinks = widget.websiteData['maxLinks'] ?? 2;
    _isSponsored = widget.websiteData['isSponsored'] ?? false;
    _basePrice = (widget.websiteData['basePricing'] ?? 0).toDouble();
    _specialTopicsAdditionalPrice =
        (widget.websiteData['specialTopicsAdditionalPrice'] ?? 0).toDouble();
    _hasSpecialTopicsPricing =
        widget.websiteData['hasSpecialTopicsPricing'] ?? false;
    _backlinkType = widget.websiteData['backlinkType'] ?? 'dofollow';
    _dofollowCost = widget.websiteData['dofollowCost'] != null
        ? (widget.websiteData['dofollowCost'] as num).toDouble()
        : null;
    _spamScore = widget.websiteData['spamScore'] != null
        ? (widget.websiteData['spamScore'] as num).toDouble()
        : null;
    _calculateTotalPrice();
    _contentController.addListener(_updateWordCount);
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _linksController.dispose();
    _notesController.dispose();
    _contentController.removeListener(_updateWordCount);
    super.dispose();
  }

  void _updateWordCount() {
    final text = _contentController.text;
    final words = text.isNotEmpty ? text.split(RegExp(r'\s+')) : [];
    if (mounted) {
      setState(() {
        _wordCount = words.length;
        if (_wordCount > _wordCountMax) {
          _contentController.text =
              text.split(RegExp(r'\s+')).take(_wordCountMax).join(' ');
          _contentController.selection = TextSelection.fromPosition(
              TextPosition(offset: _contentController.text.length));
        }
      });
    }
  }

  double publisherPrice = 0;

  void _calculateTotalPrice() {
    publisherPrice = _basePrice;

    // Calculate base price with add-ons
    if (_hasSpecialTopicsPricing) {
      publisherPrice += _specialTopicsAdditionalPrice;
    }
    if (_backlinkType == 'dofollow') {
      if (_dofollowCost != null && _dofollowCost > 0) {
        // Use specific dofollow cost if available
        publisherPrice += _dofollowCost;
      }
    }

    // Calculate commission (50% of the publisher price)
    double commission = publisherPrice * 0.5;

    // Total price is publisher price + commission
    _totalPrice = publisherPrice + commission;

    if (mounted) {
      setState(() {});
    }
  }

  Future<double> _getBuyerFunds() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) throw Exception('User not authenticated');

    try {
      final doc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .get();
      return (doc.data()?['buyerFunds'] ?? 0.0).toDouble();
    } catch (e) {
      throw Exception('Failed to fetch balance: $e');
    }
  }

// Update _submitOrder listener for cancellations
  Future<void> _submitOrder() async {
    if (!_formKey.currentState!.validate()) return;

    if (mounted) setState(() => _isLoading = true);

    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception('User not authenticated');

      final buyerFunds = await _getBuyerFunds();
      if (buyerFunds < _totalPrice) {
        if (mounted) {
          setState(() => _isLoading = false);
          _showInsufficientFundsDialog(buyerFunds);
        }
        return;
      }

      final links =
          _linksController.text.split(',').map((e) => e.trim()).toList();

      // Generate GPL format order ID
      final orderId = OrderIdGenerator.generateOrderId();

      final order = OrderModel(
        orderId: orderId,
        websiteId: widget.websiteId,
        buyerId: user.uid,
        websiteUrl: widget.websiteData['url'] ?? '',
        websiteDomainName: widget.websiteData['domainName'] ?? '',
        websiteLanguage: widget.websiteData['language'] ?? '',
        websiteCategories:
            List<String>.from(widget.websiteData['categories'] ?? []),
        postTitle: _titleController.text,
        postContent: _contentController.text,
        wordCount: _wordCount,
        links: links,
        backlinkType: _backlinkType,
        isSponsored: _isSponsored,
        basePrice: publisherPrice,
        specialTopicsAdditionalPrice:
            _hasSpecialTopicsPricing ? _specialTopicsAdditionalPrice : 0.0,
        totalPrice: _totalPrice,
        status: 'Pending',
        orderDate: Timestamp.now(),
        approvalDate: null,
        completionDate: null,
        paymentStatus: 'Pending',
        paymentId: null,
        notes: _notesController.text.isEmpty ? null : _notesController.text,
        publisherId: widget.websiteData['publisherId'] ?? '',
        rejectionReason: null,
      );

      // Use the generated order ID as the document ID
      final docRef =
          FirebaseFirestore.instance.collection('orders').doc(orderId);
      await docRef.set({
        ...order.toMap(),
        'orderDate': FieldValue.serverTimestamp(),
      });

      // Listen for payment and cancellation status
      final subscription = docRef.snapshots().listen((snapshot) {
        if (snapshot.exists && mounted) {
          final data = snapshot.data()!;
          if (data['status'] == 'Cancelled') {
            ToastHelper.showWarning(
              'Order cancelled: ${data['paymentStatus'] == 'Refunded' ? 'Funds refunded' : data['error'] ?? 'Unknown error'}',
            );
          } else if (data['paymentStatus'] == 'Paid') {
            ToastHelper.showSuccess('Order submitted and payment confirmed');
            context.go('/orders');
          } else if (data['paymentStatus'] == 'Failed') {
            ToastHelper.showError(
                'Payment failed: ${data['error'] ?? 'Unknown error'}');
          }
        }
      });
      subscription.cancel();
      if (mounted) {
        context.pop();
      }
    } catch (e) {
      ToastHelper.showError('Error submitting order: $e');
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  void _showInsufficientFundsDialog(double buyerFunds) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          'Insufficient Funds',
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.errorColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.warning_amber_rounded,
                    color: AppTheme.errorColor,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Your balance is insufficient to place this order.',
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        color: AppTheme.textPrimary,
                        height: 1.5,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            _buildPriceRow(
              'Your Balance',
              '\$${buyerFunds.toStringAsFixed(2)}',
              isHighlighted: false,
            ),
            const SizedBox(height: 8),
            _buildPriceRow(
              'Order Cost',
              '\$${_totalPrice.toStringAsFixed(2)}',
              isHighlighted: false,
            ),
            const Divider(height: 24),
            _buildPriceRow(
              'Amount Needed',
              '\$${(_totalPrice - buyerFunds).toStringAsFixed(2)}',
              isHighlighted: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: ButtonStyle(
              foregroundColor:
                  MaterialStateProperty.all(AppTheme.textSecondary),
              overlayColor: MaterialStateProperty.resolveWith<Color>((states) {
                if (states.contains(MaterialState.hovered)) {
                  return AppTheme.accentColor.withOpacity(0.05);
                }
                return Colors.transparent;
              }),
              padding: MaterialStateProperty.all(
                const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              ),
              textStyle: MaterialStateProperty.all(
                GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.push('/add-funds');
            },
            style: ButtonStyle(
              backgroundColor: MaterialStateProperty.all(AppTheme.accentColor),
              foregroundColor: MaterialStateProperty.all(Colors.white),
              padding: MaterialStateProperty.all(
                const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              ),
              shape: MaterialStateProperty.all(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              overlayColor: MaterialStateProperty.resolveWith<Color>((states) {
                if (states.contains(MaterialState.hovered)) {
                  return Colors.white.withOpacity(0.1);
                }
                return Colors.transparent;
              }),
              textStyle: MaterialStateProperty.all(
                GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  letterSpacing: 0.3,
                ),
              ),
            ),
            child: Text('Add Funds'),
          ),
        ],
        actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: LayoutBuilder(
        builder: (context, constraints) {
          final isDesktop = constraints.maxWidth > 800;
          final isTablet =
              constraints.maxWidth > 600 && constraints.maxWidth <= 800;
          final padding = isDesktop ? 32.0 : (isTablet ? 24.0 : 16.0);

          return Center(
            child: Container(
              width: 800,
              //   margin: EdgeInsets.all(isDesktop ? 32 : 16),
              padding: EdgeInsets.all(padding),
              decoration: BoxDecoration(
                color: AppTheme.surfaceColor,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.cardShadow.withOpacity(0.08),
                    spreadRadius: 0,
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
                border: Border.all(
                  color: AppTheme.borderColor.withOpacity(0.1),
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildHeader(isDesktop),
                  const SizedBox(height: 24),
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildWebsiteInfoCard(isDesktop),
                          const SizedBox(height: 24),
                          Form(
                            key: _formKey,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildTextField(
                                  controller: _titleController,
                                  label: 'Post Title',
                                  hint: 'Enter your post title',
                                  validator: (value) =>
                                      value == null || value.isEmpty
                                          ? 'Please enter a title'
                                          : null,
                                  isDesktop: isDesktop,
                                ),
                                const SizedBox(height: 20),
                                _buildMarkdownField(
                                  controller: _contentController,
                                  label: 'Post Content',
                                  hint:
                                      'Enter your post content in Markdown (min: $_wordCountMin, max: $_wordCountMax)',
                                  isDesktop: isDesktop,
                                ),
                                const SizedBox(height: 20),
                                _buildTextField(
                                  controller: _linksController,
                                  label: 'Links (comma separated)',
                                  hint:
                                      'https://example.com, https://example2.com',
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please enter at least one link';
                                    }
                                    final links = value
                                        .split(',')
                                        .map((e) => e.trim())
                                        .toList();
                                    if (links.length > _maxLinks) {
                                      return 'Maximum $_maxLinks links allowed';
                                    }
                                    for (var link in links) {
                                      if (!Uri.tryParse(link)!
                                          .hasAbsolutePath) {
                                        return 'Invalid URL: $link';
                                      }
                                    }
                                    return null;
                                  },
                                  isDesktop: isDesktop,
                                ),
                                const SizedBox(height: 20),
                                _buildBacklinkTypeSelector(isDesktop),
                                const SizedBox(height: 20),
                                _buildTextField(
                                  controller: _notesController,
                                  label: 'Additional Notes (Optional)',
                                  hint:
                                      'Any special instructions for the publisher',
                                  validator: null,
                                  maxLines: 3,
                                  isDesktop: isDesktop,
                                ),
                                const SizedBox(height: 24),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    _buildSubmitButton(isDesktop),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader(bool isDesktop) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Order Guest Post',
          style: GoogleFonts.poppins(
            fontSize: 28,
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
            letterSpacing: -0.3,
          ),
        ),
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: IconButton(
            onPressed: () => context.pop(),
            icon: Icon(Icons.close, color: AppTheme.textSecondary),
            tooltip: 'Close',
            splashRadius: 24,
            hoverColor: AppTheme.accentColor.withOpacity(0.05),
          ),
        ),
      ],
    );
  }

  Widget _buildWebsiteInfoCard(bool isDesktop) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppTheme.accentColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppTheme.accentColor.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: AppTheme.accentColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  Icons.language,
                  color: AppTheme.accentColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.websiteData['domainName'] ?? 'Website',
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                    Text(
                      widget.websiteData['url'] ?? '',
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        color: AppTheme.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          // Website metrics
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.surfaceColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppTheme.borderColor.withOpacity(0.1)),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildMetricItem(
                      'DA',
                      '${widget.websiteData['da'] ?? 'N/A'}',
                      Icons.trending_up_rounded,
                    ),
                    const SizedBox(width: 12),
                    _buildMetricItem(
                      'DR',
                      '${widget.websiteData['dr'] ?? 'N/A'}',
                      Icons.bar_chart_rounded,
                    ),
                    const SizedBox(width: 12),
                    _buildMetricItem(
                      'Traffic',
                      _formatTrafficDisplay(widget.websiteData['traffic']),
                      Icons.people_alt_rounded,
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          if (_spamScore != null) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _getSpamScoreColor(_spamScore).withOpacity(0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                    color: _getSpamScoreColor(_spamScore).withOpacity(0.2)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.security_rounded,
                    color: _getSpamScoreColor(_spamScore),
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Spam Score: ${_spamScore.toStringAsFixed(1)}%',
                          style: GoogleFonts.poppins(
                            fontSize: 15,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.textPrimary,
                          ),
                        ),
                        Text(
                          _getSpamScoreDescription(_spamScore),
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            color: AppTheme.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],
          // Price breakdown
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.surfaceColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppTheme.borderColor.withOpacity(0.1)),
            ),
            child: Column(
              children: [
                if (_hasSpecialTopicsPricing) ...[
                  _buildPriceRow(
                    'Base Content',
                    '\$${(_basePrice * 1.5).toStringAsFixed(2)}',
                    isHighlighted: false,
                  ),
                  const SizedBox(height: 8),
                  _buildPriceRow(
                    'Special Topics Fee',
                    '\$${(_specialTopicsAdditionalPrice * 1.5).toStringAsFixed(2)}',
                    isHighlighted: false,
                  ),
                ] else ...[
                  _buildPriceRow(
                    'Content Creation & Placement',
                    '\$${(_basePrice * 1.5).toStringAsFixed(2)}',
                    isHighlighted: false,
                  ),
                ],
                if (_backlinkType == 'dofollow' &&
                    _dofollowCost != null &&
                    _dofollowCost > 0) ...[
                  const SizedBox(height: 8),
                  _buildPriceRow(
                    'Dofollow Link Premium',
                    '\$${(_dofollowCost * 1.5).toStringAsFixed(2)}',
                    isHighlighted: false,
                  ),
                ],
                const Divider(height: 24),
                _buildPriceRow(
                  'Total Price',
                  '\$${_totalPrice.toStringAsFixed(2)}',
                  isHighlighted: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceRow(String label, String value,
      {required bool isHighlighted}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: GoogleFonts.inter(
            fontSize: 15,
            fontWeight: isHighlighted ? FontWeight.w600 : FontWeight.w400,
            color:
                isHighlighted ? AppTheme.textPrimary : AppTheme.textSecondary,
          ),
        ),
        Text(
          value,
          style: GoogleFonts.inter(
            fontSize: 15,
            fontWeight: isHighlighted ? FontWeight.w600 : FontWeight.w400,
            color: isHighlighted ? AppTheme.accentColor : AppTheme.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    String? Function(String?)? validator,
    int maxLines = 1,
    required bool isDesktop,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
            height: 1.5,
          ),
        ),
        const SizedBox(height: 10),
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: GoogleFonts.inter(
              fontSize: 15,
              color: AppTheme.textLight,
              height: 1.5,
            ),
            filled: true,
            fillColor: AppTheme.componentBackColor,
            border: OutlineInputBorder(
              borderSide: BorderSide.none,
              borderRadius: BorderRadius.circular(12),
            ),
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide.none,
              borderRadius: BorderRadius.circular(12),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(color: AppTheme.accentColor, width: 1.5),
              borderRadius: BorderRadius.circular(12),
            ),
            errorBorder: OutlineInputBorder(
              borderSide: BorderSide(color: AppTheme.errorColor, width: 1.5),
              borderRadius: BorderRadius.circular(12),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderSide: BorderSide(color: AppTheme.errorColor, width: 1.5),
              borderRadius: BorderRadius.circular(12),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            errorStyle: GoogleFonts.inter(
              fontSize: 13,
              color: AppTheme.errorColor,
              height: 1.4,
            ),
          ),
          style: GoogleFonts.inter(
            fontSize: 15,
            color: AppTheme.textPrimary,
            height: 1.5,
          ),
          validator: validator,
          cursorColor: AppTheme.accentColor,
        ),
      ],
    );
  }

  Widget _buildMarkdownField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required bool isDesktop,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Text(
                  label,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                    height: 1.5,
                  ),
                ),
                const SizedBox(width: 16),
                _buildWordCount(isDesktop),
              ],
            ),
            Row(
              children: [
                Text(
                  'Edit',
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    color: !_isPreviewMode
                        ? AppTheme.accentColor
                        : AppTheme.textSecondary,
                    fontWeight:
                        !_isPreviewMode ? FontWeight.w500 : FontWeight.w400,
                  ),
                ),
                Switch(
                  value: _isPreviewMode,
                  onChanged: (value) {
                    if (mounted) {
                      setState(() => _isPreviewMode = value);
                    }
                  },
                  activeColor: AppTheme.accentColor,
                  activeTrackColor: AppTheme.accentColor.withOpacity(0.3),
                  inactiveTrackColor: AppTheme.componentBackColor,
                  inactiveThumbColor: AppTheme.accentColor,
                ),
                Text(
                  'Preview',
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    color: _isPreviewMode
                        ? AppTheme.accentColor
                        : AppTheme.textSecondary,
                    fontWeight:
                        _isPreviewMode ? FontWeight.w500 : FontWeight.w400,
                  ),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 10),
        Container(
          constraints: BoxConstraints(minHeight: isDesktop ? 300 : 200),
          decoration: BoxDecoration(
            color: AppTheme.componentBackColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppTheme.borderColor.withOpacity(0.1)),
          ),
          child: _isPreviewMode
              ? Padding(
                  padding: const EdgeInsets.all(20),
                  child: MarkdownBody(
                    data: controller.text.isEmpty
                        ? 'Nothing to preview yet'
                        : controller.text,
                    styleSheet: MarkdownStyleSheet(
                      p: GoogleFonts.inter(
                        fontSize: 15,
                        color: AppTheme.textPrimary,
                        height: 1.6,
                      ),
                      h1: GoogleFonts.poppins(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimary,
                        height: 1.3,
                      ),
                      h2: GoogleFonts.poppins(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimary,
                        height: 1.3,
                      ),
                      h3: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimary,
                        height: 1.3,
                      ),
                      blockquote: GoogleFonts.inter(
                        fontSize: 15,
                        color: AppTheme.textSecondary,
                        height: 1.6,
                        fontStyle: FontStyle.italic,
                      ),
                      code: GoogleFonts.sourceCodePro(
                        fontSize: 14,
                        color: AppTheme.textPrimary,
                        backgroundColor: AppTheme.borderColor.withOpacity(0.2),
                      ),
                      codeblockDecoration: BoxDecoration(
                        color: AppTheme.borderColor.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      listBullet: GoogleFonts.inter(
                        fontSize: 15,
                        color: AppTheme.textPrimary,
                        height: 1.6,
                      ),
                    ),
                  ),
                )
              : Column(
                  children: [
                    TextFormField(
                      controller: controller,
                      maxLines: isDesktop ? 20 : 15,
                      decoration: InputDecoration(
                        hintText: hint,
                        hintStyle: GoogleFonts.inter(
                          fontSize: 15,
                          color: AppTheme.textLight,
                          height: 1.5,
                        ),
                        filled: true,
                        fillColor: AppTheme.componentBackColor,
                        border: OutlineInputBorder(
                          borderSide: BorderSide.none,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide.none,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                              color: AppTheme.accentColor, width: 1.5),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        errorBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                              color: AppTheme.errorColor, width: 1.5),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        focusedErrorBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                              color: AppTheme.errorColor, width: 1.5),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        contentPadding: const EdgeInsets.all(20),
                        errorStyle: GoogleFonts.inter(
                          fontSize: 13,
                          color: AppTheme.errorColor,
                          height: 1.4,
                        ),
                      ),
                      style: GoogleFonts.inter(
                        fontSize: 15,
                        color: AppTheme.textPrimary,
                        height: 1.5,
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter content';
                        }
                        if (_wordCount < _wordCountMin) {
                          return 'Content must be at least $_wordCountMin words';
                        }
                        return null;
                      },
                      cursorColor: AppTheme.accentColor,
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: AppTheme.surfaceColor,
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(12),
                          bottomRight: Radius.circular(12),
                        ),
                        border: Border(
                          top: BorderSide(
                              color: AppTheme.borderColor.withOpacity(0.1)),
                        ),
                      ),
                      child: MarkdownToolbar(
                        controller: controller,
                        useIncludedTextField: false,
                        backgroundColor: Colors.transparent,
                        iconColor: AppTheme.textSecondary,
                        dropdownTextColor: AppTheme.textPrimary,
                      ),
                    ),
                  ],
                ),
        ),
      ],
    );
  }

  Widget _buildBacklinkTypeSelector(bool isDesktop) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Backlink Type',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimary,
                height: 1.5,
              ),
            ),
            Text(
              _dofollowCost != null && _dofollowCost > 0
                  ? 'Fixed price of \$${_dofollowCost.toStringAsFixed(2)} for dofollow'
                  : 'Dofollow links boost SEO value',
              style: GoogleFonts.inter(
                fontSize: 14,
                color: AppTheme.textSecondary,
                height: 1.5,
              ),
            ),
          ],
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: AppTheme.componentBackColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppTheme.borderColor.withOpacity(0.1)),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _backlinkType,
              items: [
                DropdownMenuItem(
                  value: 'dofollow',
                  child: Text(
                    'Dofollow',
                    style: GoogleFonts.inter(
                      fontSize: 15,
                      color: AppTheme.textPrimary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                DropdownMenuItem(
                  value: 'nofollow',
                  child: Text(
                    'Nofollow',
                    style: GoogleFonts.inter(
                      fontSize: 15,
                      color: AppTheme.textPrimary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
              onChanged: (value) {
                if (mounted) {
                  setState(() {
                    _backlinkType = value!;
                    _calculateTotalPrice();
                  });
                }
              },
              icon: Icon(Icons.arrow_drop_down, color: AppTheme.textSecondary),
              isDense: true,
              borderRadius: BorderRadius.circular(12),
              dropdownColor: AppTheme.surfaceColor,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildWordCount(bool isDesktop) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: _wordCount < _wordCountMin
            ? AppTheme.errorColor.withOpacity(0.1)
            : AppTheme.accentColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        'Word Count: $_wordCount / $_wordCountMax (Min: $_wordCountMin)',
        style: GoogleFonts.inter(
          fontSize: isDesktop ? 14 : 13,
          fontWeight: FontWeight.w500,
          color: _wordCount < _wordCountMin
              ? AppTheme.errorColor
              : AppTheme.accentColor,
        ),
      ),
    );
  }

  // Helper method to build a metric item
  Widget _buildMetricItem(String label, String value, IconData icon) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppTheme.accentColor.withOpacity(0.05),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: AppTheme.accentColor,
              size: 20,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimary,
              ),
            ),
            Text(
              label,
              style: GoogleFonts.inter(
                fontSize: 14,
                color: AppTheme.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to format traffic for display
  String _formatTrafficDisplay(dynamic traffic) {
    if (traffic == null) return 'N/A';

    int trafficValue = 0;
    if (traffic is int) {
      trafficValue = traffic;
    } else if (traffic is double) {
      trafficValue = traffic.toInt();
    } else if (traffic is String) {
      trafficValue = int.tryParse(traffic) ?? 0;
    }

    if (trafficValue >= 1000000) {
      return '${(trafficValue / 1000000).toStringAsFixed(1)}M';
    } else if (trafficValue >= 1000) {
      return '${(trafficValue / 1000).toStringAsFixed(1)}K';
    }
    return trafficValue.toString();
  }

  // Helper method to get color based on spam score
  Color _getSpamScoreColor(double score) {
    if (score < 5) {
      return Colors.green;
    } else if (score < 15) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  // Helper method to get description based on spam score
  String _getSpamScoreDescription(double score) {
    if (score < 5) {
      return 'Excellent - This website has a very low spam score';
    } else if (score < 15) {
      return 'Moderate - This website has an acceptable spam score';
    } else {
      return 'High - This website has a high spam score, proceed with caution';
    }
  }

  Widget _buildSubmitButton(bool isDesktop) {
    return SizedBox(
      width: 200,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _submitOrder,
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.resolveWith<Color>((states) {
            if (states.contains(MaterialState.disabled)) {
              return AppTheme.accentColor.withOpacity(0.5);
            }
            return AppTheme.accentColor;
          }),
          foregroundColor: MaterialStateProperty.all(Colors.white),
          padding: MaterialStateProperty.all(
            EdgeInsets.symmetric(vertical: isDesktop ? 18 : 16),
          ),
          shape: MaterialStateProperty.all(
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
          elevation: MaterialStateProperty.all(0),
          overlayColor: MaterialStateProperty.resolveWith<Color>((states) {
            if (states.contains(MaterialState.hovered)) {
              return Colors.white.withOpacity(0.1);
            }
            if (states.contains(MaterialState.pressed)) {
              return Colors.white.withOpacity(0.2);
            }
            return Colors.transparent;
          }),
        ),
        child: _isLoading
            ? SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2.5,
                ),
              )
            : Text(
                'Submit Order',
                style: GoogleFonts.inter(
                  fontSize: isDesktop ? 16 : 15,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.3,
                ),
              ),
      ),
    );
  }
}
