import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:guest_posts/core/services/auth_service.dart';
import 'package:guest_posts/core/services/cRUD_services.dart';
import 'package:guest_posts/core/theme/app_theme.dart';
import 'package:guest_posts/core/utils/toast_helper.dart';
import 'package:guest_posts/features/buyer_acc/earnings_page/testPaypal.dart';
import 'package:guest_posts/features/buyer_acc/earnings_page/stripe_web_payment_dialog_fixed.dart';
import 'package:guest_posts/core/models/offer_model.dart';
import 'package:guest_posts/core/models/policy_model.dart';
import 'package:guest_posts/core/services/policy_service.dart';
import 'package:guest_posts/core/widgets/policy_acceptance_dialog.dart';

class BuyerFundsPage extends StatefulWidget {
  final AuthService authService;

  const BuyerFundsPage({super.key, required this.authService});

  @override
  State<BuyerFundsPage> createState() => _BuyerFundsPageState();
}

class _BuyerFundsPageState extends State<BuyerFundsPage>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true; // Keep the state alive

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirestoreService _firestoreService = FirestoreService();
  bool _isLoading = false;
  final TextEditingController _amountController = TextEditingController();
  String _selectedPaymentMethod = 'Stripe';

  // Offers related variables
  List<OfferModel> _availableOffers = [];

  // Predefined withdrawal amounts from $25 to $5000
  final List<double> _withdrawalAmounts = [
    25,
    50,
    75,
    100,
    150,
    200,
    250,
    300,
    400,
    500,
    750,
    1000,
    1500,
    2000,
    2500,
    3000,
    4000,
    5000
  ];
  double _selectedAmount = 100; // Default selected amount

  Future<void> _showStripePaymentDialog() async {
    if (_selectedAmount <= 0) {
      _showMessage('Please select a valid amount');
      return;
    }

    setState(() => _isLoading = true);

    try {
      await showDialog(
        context: context,
        builder: (BuildContext context) {
          return StripeWebPaymentDialog(
            amount: _selectedAmount,
            currency: 'USD',
            onPaymentSuccess: (String paymentIntentId) {
              _verifyStripePayment(paymentIntentId, _selectedAmount);
            },
            onPaymentError: (String error) {
              setState(() => _isLoading = false);
              _showMessage('Payment failed: $error');
            },
            onPaymentCancelled: () {
              setState(() => _isLoading = false);
              _showMessage('Payment cancelled');
            },
          );
        },
      );
    } catch (e) {
      setState(() => _isLoading = false);
      _showMessage('Error showing payment dialog: $e');
    }
  }

  Future<void> _verifyStripePayment(
      String paymentIntentId, double amount) async {
    setState(() => _isLoading = true);
    try {
      // Calculate bonus amount if any
      final double bonusAmount = _calculateBonusAmount();
      final double totalAmount = amount + bonusAmount;

      final callable =
          FirebaseFunctions.instance.httpsCallable('verifyStripePayment');
      final response = await callable.call({
        'paymentIntentId': paymentIntentId,
        'amount': amount,
        'bonusAmount': bonusAmount,
        'totalAmount': totalAmount
      });

      if (response.data['success']) {
        if (bonusAmount > 0) {
          _showMessage(
              'Funds added via Stripe! You received a bonus of \$${bonusAmount.toStringAsFixed(2)}. Total funds added: \$${totalAmount.toStringAsFixed(2)}');
        } else {
          _showMessage('Funds added via Stripe! Funds will reflect shortly.');
        }
      } else {
        throw Exception('Stripe verification did not return success.');
      }
    } catch (e) {
      debugPrint('Stripe verification error: $e');
      _showMessage('Stripe payment verification failed: $e');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  // Fetch available offers from Firestore
  Future<void> _fetchOffers() async {
    if (mounted) {
      setState(() {
        // Loading state
      });
    }

    try {
      final QuerySnapshot<Map<String, dynamic>> snapshot =
          await FirebaseFirestore.instance
              .collection('offers')
              .where('isActive', isEqualTo: true)
              .get();

      if (mounted) {
        setState(() {
          _availableOffers = snapshot.docs
              .map((doc) => OfferModel.fromMap(doc.data(), doc.id))
              .where((offer) => offer.isValid())
              .toList();
        });
      }
    } catch (e) {
      debugPrint('Error fetching offers: $e');
    }
  }

  // Calculate bonus amount based on selected amount
  double _calculateBonusAmount() {
    if (_availableOffers.isEmpty) return 0.0;

    // Find the best offer for the selected amount
    double highestBonus = 0.0;

    for (var offer in _availableOffers) {
      if (_selectedAmount >= offer.minAmount) {
        double bonus = offer.calculateBonus(_selectedAmount);
        if (bonus > highestBonus) {
          highestBonus = bonus;
        }
      }
    }

    return highestBonus;
  }

  @override
  void initState() {
    super.initState();

    // Initialize amount controller with default selected amount
    _amountController.text = _selectedAmount.toString();

    // Fetch available offers
    _fetchOffers();
  }

  void _showMessage(String msg) {
    ToastHelper.showInfo(msg);
  }

  // Render PayPal button

  // Capture PayPal payment
  Future<void> _capturePaypalPayment(String orderId, double amount) async {
    setState(() => _isLoading = true);
    try {
      // Calculate bonus amount if any
      final double bonusAmount = _calculateBonusAmount();
      final double totalAmount = amount + bonusAmount;

      final callable =
          FirebaseFunctions.instance.httpsCallable('confirmPaypalPayment');
      final response = await callable.call({
        'orderId': orderId,
        'amount': amount,
        'bonusAmount': bonusAmount,
        'totalAmount': totalAmount
      });

      if (response.data['success']) {
        if (bonusAmount > 0) {
          _showMessage(
              'Funds added via PayPal! You received a bonus of \$${bonusAmount.toStringAsFixed(2)}. Total funds added: \$${totalAmount.toStringAsFixed(2)}');
        } else {
          _showMessage('Funds added via PayPal! Funds will reflect shortly.');
        }
        // No need to clear the dropdown selection
      } else {
        throw Exception('PayPal capture did not return success.');
      }
    } catch (e) {
      debugPrint('PayPal capture error: $e');
      _showMessage('PayPal payment capture failed: $e');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _addFunds() async {
    final amount = _selectedAmount;
    if (amount <= 0) {
      _showMessage('Please select a valid amount');
      return;
    }

    // Check policy acceptance before processing payment
    final policyService = PolicyService();
    final requiredPolicies =
        await policyService.getRequiredPoliciesForContext('payment');

    if (requiredPolicies.isNotEmpty) {
      final accepted = await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (context) => PolicyAcceptanceDialog(
          context: 'payment',
          title: 'Payment Terms Required',
          description:
              'Before processing your payment, please accept our payment-related policies.',
        ),
      );

      if (accepted != true) {
        _showMessage('Payment cancelled - policies not accepted');
        return;
      }
    }

    if (mounted) {
      setState(() => _isLoading = true);
    }
    try {
      if (_selectedPaymentMethod == 'Stripe') {
        await _showStripePaymentDialog();
      } else if (_selectedPaymentMethod == 'PayPal') {
        if (amount > 0) {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return PayPalDialogWidget(
                amount: _selectedAmount,
                currency: 'USD',
                onPaymentSuccess: (String orderId) {
                  // Payment successful
                  _capturePaypalPayment(orderId, _selectedAmount);
                  setState(() => _isLoading = false);

                  // Handle successful payment
                },
                onPaymentError: (String error) {
                  // Payment failed
                  setState(() => _isLoading = false);
                  _showMessage('Payment failed: $error');
                },
                onPaymentCancelled: () {
                  setState(() => _isLoading = false);
                  _showMessage('Payment cancelled');
                },
              );
            },
          ).then((value) => setState(() => _isLoading = false));
          setState(() => _isLoading = true);
        } else {
          _showMessage('Please select a valid amount.');
          setState(() => _isLoading = false);
        }
        return; // Wait for redirect to complete
      }
    } finally {
      if (mounted && _selectedPaymentMethod != 'PayPal') {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    if (_auth.currentUser == null) {
      return const Scaffold(
          body: Center(child: Text('Please sign in to manage funds')));
    }

    return Container(
      color: AppTheme.backgroundColor,
      child: StreamBuilder<DocumentSnapshot<Map<String, dynamic>>>(
        stream: _firestoreService.streamDocument(
            collectionPath: 'users', documentId: _auth.currentUser!.uid),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (!snapshot.hasData || snapshot.data?.data() == null) {
            debugPrint('Snapshot data is null or missing');
            return const Center(child: Text('User data not found'));
          }

          final userData = snapshot.data!.data()!;
          if (userData['isPublisher'] == true) {
            return const Center(child: Text('This page is for buyers only'));
          }

          final buyerFunds =
              (userData['buyerFunds'] as num?)?.toDouble() ?? 0.0;
          final reservedFunds =
              (userData['reservedFunds'] as num?)?.toDouble() ?? 0.0;

          return SingleChildScrollView(
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              // Offers Banner at the top
              if (_availableOffers.isNotEmpty) _buildOffersBanner(),

              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: double.infinity,
                      child: Builder(
                        builder: (context) {
                          try {
                            return GridView.count(
                              padding: const EdgeInsets.all(0),
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              crossAxisCount:
                                  MediaQuery.of(context).size.width > 600
                                      ? 3
                                      : 1,
                              mainAxisSpacing: 16,
                              crossAxisSpacing: 16,
                              childAspectRatio: 2,
                              children: [
                                _buildMetricCard(
                                    'Available Funds',
                                    buyerFunds,
                                    FontAwesomeIcons.wallet,
                                    Colors.white,
                                    'Ready to spend'),
                                _buildMetricCard(
                                    'Reserved Funds',
                                    reservedFunds,
                                    FontAwesomeIcons.lock,
                                    Colors.white,
                                    'Pending orders'),
                                Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(16),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.grey.withOpacity(0.1),
                                        blurRadius: 10,
                                        offset: const Offset(0, 4),
                                      )
                                    ],
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          const Text(
                                            'Add Funds',
                                            style: TextStyle(
                                              fontSize: 20,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          Icon(
                                              FontAwesomeIcons
                                                  .moneyBillTransfer,
                                              color: AppTheme.accentColor),
                                        ],
                                      ),
                                      const SizedBox(height: 16),
                                      Container(
                                        decoration: BoxDecoration(
                                          color: Colors.grey[100],
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          border: Border.all(
                                              color: Colors.grey.shade400),
                                        ),
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 12),
                                        child: Row(
                                          children: [
                                            const Icon(Icons.attach_money,
                                                color: Colors.grey),
                                            const SizedBox(width: 8),
                                            Expanded(
                                              child:
                                                  DropdownButtonHideUnderline(
                                                child: DropdownButton<double>(
                                                  value: _selectedAmount,
                                                  hint: const Text(
                                                      'Select Amount'),
                                                  isExpanded: true,
                                                  items: _withdrawalAmounts
                                                      .map((amount) =>
                                                          DropdownMenuItem(
                                                            value: amount,
                                                            child: Text(
                                                                '\$${amount.toStringAsFixed(0)}'),
                                                          ))
                                                      .toList(),
                                                  onChanged: (value) {
                                                    if (mounted &&
                                                        value != null) {
                                                      setState(() {
                                                        _selectedAmount = value;
                                                        _amountController.text =
                                                            value.toString();
                                                      });
                                                    }
                                                  },
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),

                                      // Display available offers section
                                      // if (_availableOffers.isNotEmpty) ...[
                                      //   const SizedBox(height: 16),
                                      //   Container(
                                      //     width: double.infinity,
                                      //     decoration: BoxDecoration(
                                      //       color: Colors.amber.shade50,
                                      //       borderRadius:
                                      //           BorderRadius.circular(12),
                                      //       border: Border.all(
                                      //         color: Colors.amber.shade300,
                                      //       ),
                                      //     ),
                                      //     padding: const EdgeInsets.all(16),
                                      //     child: Column(
                                      //       crossAxisAlignment:
                                      //           CrossAxisAlignment.start,
                                      //       children: [
                                      //         Row(
                                      //           children: [
                                      //             Icon(
                                      //               Icons.local_offer,
                                      //               color:
                                      //                   Colors.amber.shade700,
                                      //             ),
                                      //             const SizedBox(width: 8),
                                      //             const Text(
                                      //               'Available Offers',
                                      //               style: TextStyle(
                                      //                 fontWeight:
                                      //                     FontWeight.bold,
                                      //                 fontSize: 16,
                                      //               ),
                                      //             ),
                                      //           ],
                                      //         ),
                                      //         const SizedBox(height: 12),
                                      //         ..._availableOffers.map((offer) {
                                      //           final bool isApplicable =
                                      //               _selectedAmount >=
                                      //                   offer.minAmount;
                                      //           final double bonusAmount =
                                      //               offer.calculateBonus(
                                      //                   _selectedAmount);

                                      //           return Container(
                                      //             margin: const EdgeInsets.only(
                                      //                 bottom: 8),
                                      //             padding:
                                      //                 const EdgeInsets.all(12),
                                      //             decoration: BoxDecoration(
                                      //               color: isApplicable
                                      //                   ? Colors.white
                                      //                   : Colors.grey.shade100,
                                      //               borderRadius:
                                      //                   BorderRadius.circular(
                                      //                       8),
                                      //               border: Border.all(
                                      //                 color: isApplicable
                                      //                     ? Colors
                                      //                         .green.shade300
                                      //                     : Colors
                                      //                         .grey.shade300,
                                      //               ),
                                      //             ),
                                      //             child: Column(
                                      //               crossAxisAlignment:
                                      //                   CrossAxisAlignment
                                      //                       .start,
                                      //               children: [
                                      //                 Row(
                                      //                   mainAxisAlignment:
                                      //                       MainAxisAlignment
                                      //                           .spaceBetween,
                                      //                   children: [
                                      //                     Text(
                                      //                       offer.title,
                                      //                       style:
                                      //                           const TextStyle(
                                      //                         fontWeight:
                                      //                             FontWeight
                                      //                                 .bold,
                                      //                       ),
                                      //                     ),
                                      //                     Container(
                                      //                       padding:
                                      //                           const EdgeInsets
                                      //                               .symmetric(
                                      //                         horizontal: 8,
                                      //                         vertical: 4,
                                      //                       ),
                                      //                       decoration:
                                      //                           BoxDecoration(
                                      //                         color: isApplicable
                                      //                             ? Colors.green
                                      //                                 .shade100
                                      //                             : Colors.grey
                                      //                                 .shade200,
                                      //                         borderRadius:
                                      //                             BorderRadius
                                      //                                 .circular(
                                      //                                     12),
                                      //                       ),
                                      //                       child: Text(
                                      //                         '${offer.percentage.toStringAsFixed(0)}% Bonus',
                                      //                         style: TextStyle(
                                      //                           color: isApplicable
                                      //                               ? Colors
                                      //                                   .green
                                      //                                   .shade800
                                      //                               : Colors
                                      //                                   .grey
                                      //                                   .shade700,
                                      //                           fontWeight:
                                      //                               FontWeight
                                      //                                   .bold,
                                      //                           fontSize: 12,
                                      //                         ),
                                      //                       ),
                                      //                     ),
                                      //                   ],
                                      //                 ),
                                      //                 const SizedBox(height: 4),
                                      //                 Text(
                                      //                   offer.description,
                                      //                   style: TextStyle(
                                      //                     color: Colors
                                      //                         .grey.shade700,
                                      //                     fontSize: 13,
                                      //                   ),
                                      //                 ),
                                      //                 const SizedBox(height: 8),
                                      //                 Row(
                                      //                   mainAxisAlignment:
                                      //                       MainAxisAlignment
                                      //                           .spaceBetween,
                                      //                   children: [
                                      //                     Text(
                                      //                       'Min. Amount: \$${offer.minAmount.toStringAsFixed(0)}',
                                      //                       style: TextStyle(
                                      //                         color: Colors.grey
                                      //                             .shade600,
                                      //                         fontSize: 12,
                                      //                       ),
                                      //                     ),
                                      //                     if (isApplicable)
                                      //                       Text(
                                      //                         'You get: \$${bonusAmount.toStringAsFixed(2)} extra',
                                      //                         style:
                                      //                             const TextStyle(
                                      //                           color: Colors
                                      //                               .green,
                                      //                           fontWeight:
                                      //                               FontWeight
                                      //                                   .bold,
                                      //                           fontSize: 13,
                                      //                         ),
                                      //                       ),
                                      //                   ],
                                      //                 ),
                                      //                 if (offer.validUntil !=
                                      //                     null) ...[
                                      //                   const SizedBox(
                                      //                       height: 4),
                                      //                   Text(
                                      //                     'Valid until: ${offer.validUntil!.toDate().toString().substring(0, 10)}',
                                      //                     style: TextStyle(
                                      //                       color: Colors
                                      //                           .grey.shade600,
                                      //                       fontSize: 12,
                                      //                     ),
                                      //                   ),
                                      //                 ],
                                      //               ],
                                      //             ),
                                      //           );
                                      //         }),

                                      //         // Summary of bonus
                                      //         if (_calculateBonusAmount() >
                                      //             0) ...[
                                      //           const SizedBox(height: 12),
                                      //           Container(
                                      //             padding:
                                      //                 const EdgeInsets.all(12),
                                      //             decoration: BoxDecoration(
                                      //               color: Colors.green.shade50,
                                      //               borderRadius:
                                      //                   BorderRadius.circular(
                                      //                       8),
                                      //               border: Border.all(
                                      //                 color:
                                      //                     Colors.green.shade300,
                                      //               ),
                                      //             ),
                                      //             child: Row(
                                      //               mainAxisAlignment:
                                      //                   MainAxisAlignment
                                      //                       .spaceBetween,
                                      //               children: [
                                      //                 const Text(
                                      //                   'Total bonus you will receive:',
                                      //                   style: TextStyle(
                                      //                     fontWeight:
                                      //                         FontWeight.bold,
                                      //                   ),
                                      //                 ),
                                      //                 Text(
                                      //                   '\$${_calculateBonusAmount().toStringAsFixed(2)}',
                                      //                   style: const TextStyle(
                                      //                     fontWeight:
                                      //                         FontWeight.bold,
                                      //                     color: Colors.green,
                                      //                     fontSize: 16,
                                      //                   ),
                                      //                 ),
                                      //               ],
                                      //             ),
                                      //           ),
                                      //         ],
                                      //       ],
                                      //     ),
                                      //   ),
                                      // ],
                                      const SizedBox(height: 16),
                                      Container(
                                        decoration: BoxDecoration(
                                          color: Colors.grey[100],
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 12),
                                        child: DropdownButtonHideUnderline(
                                          child: DropdownButton<String>(
                                            value: _selectedPaymentMethod,
                                            items: ['Stripe', 'PayPal']
                                                .map((method) =>
                                                    DropdownMenuItem(
                                                      value: method,
                                                      child: Row(
                                                        children: [
                                                          Icon(
                                                            method == 'PayPal'
                                                                ? FontAwesomeIcons
                                                                    .paypal
                                                                : FontAwesomeIcons
                                                                    .creditCard,
                                                            size: 18,
                                                            color: method ==
                                                                    'PayPal'
                                                                ? Colors.blue
                                                                : AppTheme
                                                                    .accentColor,
                                                          ),
                                                          const SizedBox(
                                                              width: 10),
                                                          Text(method),
                                                        ],
                                                      ),
                                                    ))
                                                .toList(),
                                            onChanged: (value) {
                                              if (mounted) {
                                                setState(() =>
                                                    _selectedPaymentMethod =
                                                        value ?? 'Stripe');
                                              }
                                            },
                                            isExpanded: true,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(height: 20),
                                      _isLoading
                                          ? const Center(
                                              child: CircularProgressIndicator(
                                                  color: AppTheme.accentColor))
                                          : SizedBox(
                                              width: double.infinity,
                                              height: 50,
                                              child: ElevatedButton(
                                                onPressed: _addFunds,
                                                style: ElevatedButton.styleFrom(
                                                  backgroundColor:
                                                      AppTheme.accentColor,
                                                  foregroundColor: Colors.black,
                                                  elevation: 2,
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            12),
                                                  ),
                                                ),
                                                child: Text(
                                                  'Add Funds via $_selectedPaymentMethod',
                                                  style: const TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      fontSize: 16,
                                                      color: Colors.white),
                                                ),
                                              ),
                                            ),
                                    ],
                                  ),
                                ),
                              ],
                            );
                          } catch (e) {
                            debugPrint('GridView error: $e');
                            return const Center(
                                child: Text('Error loading funds'));
                          }
                        },
                      ),
                    ),
                    const SizedBox(height: 16),
                    PaymentHistorySection(
                        firestoreService: _firestoreService,
                        userId: _auth.currentUser!.uid),
                  ],
                ),
              ),
            ]),
          );
        },
      ),
    );
  }

  Widget _buildOffersBanner() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.accentColor,
            Color(0xFF2A2A2A),
          ],
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: AppTheme.accentColor.withOpacity(0.2),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    FontAwesomeIcons.gift,
                    color: Colors.white,
                    size: 22,
                  ),
                ),
                const SizedBox(width: 16),
                const Text(
                  'Exclusive Offers',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Cairo',
                    color: Colors.white,
                  ),
                ),
                const Spacer(),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.access_time,
                        color: Colors.white,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      const Text(
                        'Limited Time',
                        style: TextStyle(
                          fontFamily: 'Cairo',
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 200,
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              scrollDirection: Axis.horizontal,
              itemCount: _availableOffers.length,
              itemBuilder: (context, index) {
                final offer = _availableOffers[index];
                // Calculate bonus amount for this offer
                final bonusAmount = offer.calculateBonus(_selectedAmount);
                final bool isApplicable = _selectedAmount >= offer.minAmount;

                return Container(
                  width: 320,
                  margin: const EdgeInsets.only(right: 16, bottom: 20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.08),
                        blurRadius: 15,
                        offset: const Offset(0, 5),
                      ),
                    ],
                    border: Border.all(
                      color: isApplicable
                          ? AppTheme.accentColor.withOpacity(0.3)
                          : Colors.transparent,
                      width: 2,
                    ),
                  ),
                  child: Stack(
                    children: [
                      // Percentage badge
                      Positioned(
                        top: 0,
                        right: 24,
                        child: Container(
                          height: 50,
                          width: 60,
                          decoration: BoxDecoration(
                            color: AppTheme.accentColor,
                            borderRadius: const BorderRadius.only(
                              bottomLeft: Radius.circular(15),
                              bottomRight: Radius.circular(15),
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: AppTheme.accentColor.withOpacity(0.3),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                '+${offer.percentage}%',
                                style: const TextStyle(
                                  fontFamily: 'Cairo',
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                ),
                              ),
                              const Text(
                                'BONUS',
                                style: TextStyle(
                                  fontFamily: 'Cairo',
                                  color: Colors.white,
                                  fontWeight: FontWeight.w500,
                                  fontSize: 10,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      // Card content
                      Padding(
                        padding: const EdgeInsets.all(18),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color:
                                        AppTheme.accentColor.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Icon(
                                    FontAwesomeIcons.coins,
                                    size: 16,
                                    color: AppTheme.accentColor,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    offer.title,
                                    style: const TextStyle(
                                      fontFamily: 'Cairo',
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black87,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              offer.description,
                              style: TextStyle(
                                fontFamily: 'Cairo',
                                fontSize: 14,
                                color: Colors.grey.shade700,
                                height: 1.4,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const Spacer(),

                            // Bonus calculation
                            // if (isApplicable)
                            //   Container(
                            //     width: double.infinity,
                            //     padding: const EdgeInsets.symmetric(
                            //         vertical: 10, horizontal: 16),
                            //     margin: const EdgeInsets.only(bottom: 16),
                            //     decoration: BoxDecoration(
                            //       color: Colors.green.shade50,
                            //       borderRadius: BorderRadius.circular(12),
                            //       border: Border.all(
                            //         color: Colors.green.shade200,
                            //         width: 1,
                            //       ),
                            //     ),
                            //     child: Row(
                            //       mainAxisAlignment:
                            //           MainAxisAlignment.spaceBetween,
                            //       children: [
                            //         Text(
                            //           'Your bonus:',
                            //           style: TextStyle(
                            //             fontFamily: 'Cairo',
                            //             fontSize: 14,
                            //             color: Colors.green.shade800,
                            //           ),
                            //         ),
                            //         Text(
                            //           '\$${bonusAmount.toStringAsFixed(2)}',
                            //           style: TextStyle(
                            //             fontFamily: 'Cairo',
                            //             fontSize: 16,
                            //             fontWeight: FontWeight.bold,
                            //             color: Colors.green.shade800,
                            //           ),
                            //         ),
                            //       ],
                            //     ),
                            //   ),

                            // Bottom info row
                            Container(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              decoration: BoxDecoration(
                                border: Border(
                                  top: BorderSide(
                                    color: Colors.grey.shade200,
                                    width: 1,
                                  ),
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(6),
                                        decoration: BoxDecoration(
                                          color: AppTheme.accentColor
                                              .withOpacity(0.1),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: Icon(
                                          Icons.attach_money,
                                          size: 14,
                                          color: AppTheme.accentColor,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        'Min: \$${offer.minAmount.toStringAsFixed(0)}',
                                        style: const TextStyle(
                                          fontFamily: 'Cairo',
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                          color: Colors.black87,
                                        ),
                                      ),
                                    ],
                                  ),
                                  if (offer.validUntil != null)
                                    Row(
                                      children: [
                                        Container(
                                          padding: const EdgeInsets.all(6),
                                          decoration: BoxDecoration(
                                            color:
                                                Colors.orange.withOpacity(0.1),
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                          child: Icon(
                                            Icons.event,
                                            size: 14,
                                            color: Colors.orange.shade700,
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          'Ends: ${offer.validUntil!.toDate().toString().substring(0, 10)}',
                                          style: TextStyle(
                                            fontFamily: 'Cairo',
                                            fontSize: 13,
                                            fontWeight: FontWeight.w500,
                                            color: Colors.orange.shade800,
                                          ),
                                        ),
                                      ],
                                    ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricCard(
      String title, double value, IconData icon, Color color, String subtitle) {
    try {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(color: color.withOpacity(0.2), blurRadius: 8)
            ]),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
              Text(title, style: const TextStyle(fontSize: 16)),
              Icon(icon, color: Colors.black54)
            ]),
            Spacer(),
            Text('\$${value.toStringAsFixed(2)}',
                style:
                    const TextStyle(fontSize: 28, fontWeight: FontWeight.bold)),
            Text(subtitle,
                style: TextStyle(
                    fontSize: 14, color: Colors.black.withOpacity(0.6))),
          ],
        ),
      );
    } catch (e) {
      debugPrint('Metric card error: $e');
      return const SizedBox.shrink();
    }
  }
}

class PaymentHistorySection extends StatefulWidget {
  final FirestoreService firestoreService;
  final String userId;

  const PaymentHistorySection(
      {super.key, required this.firestoreService, required this.userId});

  @override
  State<PaymentHistorySection> createState() => _PaymentHistorySectionState();
}

class _PaymentHistorySectionState extends State<PaymentHistorySection> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedTab = 'All';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Widget _buildTab(String title, bool isSelected) {
    return GestureDetector(
      onTap: () {
        if (mounted) {
          setState(() => _selectedTab = title);
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.accentColor : Colors.grey[200],
          borderRadius: BorderRadius.circular(10),
        ),
        child: Text(title,
            style: TextStyle(
                color: isSelected ? Colors.white : Colors.black87,
                fontWeight: FontWeight.w500)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    try {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(color: Colors.grey.withOpacity(0.1), blurRadius: 8)
            ]),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Transaction History',
                    style:
                        TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
                SizedBox(
                  width: 300,
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      labelText: 'Search by ID',
                      border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8)),
                      prefixIcon: const Icon(Icons.search),
                      filled: true,
                      fillColor: AppTheme.backgroundColor,
                    ),
                    onChanged: (value) {
                      if (mounted) {
                        setState(() {});
                      }
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                  children: ['All', 'Deposit', 'Order']
                      .map((tab) => Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: _buildTab(tab, _selectedTab == tab)))
                      .toList()),
            ),
            const SizedBox(height: 24),
            StreamBuilder<QuerySnapshot<Map<String, dynamic>>>(
              stream: widget.firestoreService.streamSubCollection(
                  collectionPath: 'users',
                  documentId: widget.userId,
                  orderBy: 'date',
                  subCollectionPath: 'history'),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
                  return const Center(child: Text('No history available'));
                }

                var history =
                    snapshot.data!.docs.map((doc) => doc.data()).toList();
                final searchQuery = _searchController.text.toLowerCase();
                history = history
                    .where((entry) =>
                        (entry['id']?.toString().toLowerCase() ?? '')
                            .contains(searchQuery) &&
                        (_selectedTab == 'All' ||
                            entry['type'] == _selectedTab) &&
                        entry['as'] == "Buyer")
                    .toList();

                return SizedBox(
                  height: 400,
                  child: SingleChildScrollView(
                    scrollDirection: Axis.vertical,
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: DataTable(
                        columnSpacing: 60,
                        columns: const [
                          DataColumn(
                              label: Text('ID',
                                  style:
                                      TextStyle(fontWeight: FontWeight.bold))),
                          DataColumn(
                              label: Text('Description',
                                  style:
                                      TextStyle(fontWeight: FontWeight.bold))),
                          DataColumn(
                              label: Text('Amount',
                                  style:
                                      TextStyle(fontWeight: FontWeight.bold))),
                          DataColumn(
                              label: Text('Status',
                                  style:
                                      TextStyle(fontWeight: FontWeight.bold))),
                          DataColumn(
                              label: Text('Date',
                                  style:
                                      TextStyle(fontWeight: FontWeight.bold))),
                          DataColumn(
                              label: Text('Details',
                                  style:
                                      TextStyle(fontWeight: FontWeight.bold))),
                        ],
                        rows: history.map((entry) {
                          try {
                            return DataRow(cells: [
                              DataCell(Text(entry['id']?.toString() ?? 'N/A')),
                              DataCell(Text(
                                  entry['description']?.toString() ?? 'N/A')),
                              DataCell(Text(
                                  '\$${((entry['amount'] as num?) ?? 0.0).toStringAsFixed(2)}',
                                  style: const TextStyle(
                                      fontWeight: FontWeight.bold))),
                              DataCell(Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: entry['status'] == 'Completed'
                                      ? Colors.green.withOpacity(0.1)
                                      : Colors.orange.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                    entry['status']?.toString() ?? 'N/A',
                                    style: TextStyle(
                                        color: entry['status'] == 'Completed'
                                            ? Colors.green
                                            : Colors.orange,
                                        fontWeight: FontWeight.bold)),
                              )),
                              DataCell(Text((entry['date'] as Timestamp?)
                                      ?.toDate()
                                      .toString()
                                      .substring(0, 10) ??
                                  'N/A')),
                              DataCell(IconButton(
                                  icon: const Icon(Icons.info_outline,
                                      color: Colors.blue),
                                  onPressed: () => _showDetailsDialog(entry))),
                            ]);
                          } catch (e) {
                            debugPrint('DataTable row error: $e');
                            return DataRow(
                                cells: List.generate(
                                    6,
                                    (_) => const DataCell(Text('Error',
                                        style: TextStyle(color: Colors.red)))));
                          }
                        }).toList(),
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      );
    } catch (e, stackTrace) {
      debugPrint('PaymentHistorySection error: $e\n$stackTrace');
      return const Center(child: Text('Error rendering history'));
    }
  }

  void _showDetailsDialog(Map<String, dynamic> entry) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        elevation: 0,
        backgroundColor: Colors.transparent,
        child: Container(
          width: 600,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Transaction Details',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.grey),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const Divider(height: 24),
              ...['id', 'description', 'amount', 'status', 'date', 'type']
                  .map((key) => Padding(
                        padding: const EdgeInsets.symmetric(vertical: 10),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              key.capitalize(),
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 15,
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: key == 'status'
                                    ? _getStatusColor(
                                        entry[key]?.toString() ?? 'N/A')
                                    : Colors.grey[100],
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                key == 'date'
                                    ? ((entry[key] as Timestamp?)
                                            ?.toDate()
                                            .toString()
                                            .substring(0, 10) ??
                                        'N/A')
                                    : key == 'amount'
                                        ? '\$${((entry[key] as num?) ?? 0.0).toStringAsFixed(2)}'
                                        : (entry[key]?.toString() ?? 'N/A'),
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  color: key == 'status'
                                      ? _getStatusTextColor(
                                          entry[key]?.toString() ?? 'N/A')
                                      : Colors.black87,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )),
              const SizedBox(height: 20),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.accentColor,
                  minimumSize: const Size(double.infinity, 45),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                onPressed: () => Navigator.pop(context),
                child: const Text(
                  'Close',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return Colors.green.withOpacity(0.2);
      case 'pending':
        return Colors.orange.withOpacity(0.2);
      case 'failed':
        return Colors.red.withOpacity(0.2);
      default:
        return Colors.grey.withOpacity(0.2);
    }
  }

  Color _getStatusTextColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'failed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}

extension StringExtension on String {
  String capitalize() =>
      "${this[0].toUpperCase()}${substring(1).toLowerCase()}";
}
