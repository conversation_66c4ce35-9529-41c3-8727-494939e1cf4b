import 'dart:math';

/// Utility class for generating order IDs in the GPL format
/// Format: "GPL" + YYYYMMDD + HHMMSS + XXX (3 random digits)
/// Example: GPL20241215143052847
class OrderIdGenerator {
  static final Random _random = Random();
  static int _counter = 0;
  static DateTime? _lastTimestamp;

  /// Generates a new order ID using the GPL format
  ///
  /// Format breakdown:
  /// - GPL: Fixed prefix (Guest Post Link)
  /// - YYYYMMDD: Current date (e.g., 20241215 for December 15, 2024)
  /// - HHMMSS: Current time in 24-hour format (e.g., 143052 for 14:30:52)
  /// - XXX: 3 random digits (000-999) with counter for uniqueness
  ///
  /// Returns a string like: GPL20241215143052847
  static String generateOrderId() {
    final now = DateTime.now();

    // Reset counter if we're in a new second
    if (_lastTimestamp == null ||
        now.difference(_lastTimestamp!).inSeconds > 0) {
      _counter = 0;
      _lastTimestamp = now;
    } else {
      _counter++;
    }

    // Format date as YYYYMMDD
    final dateComponent = '${now.year}'
        '${now.month.toString().padLeft(2, '0')}'
        '${now.day.toString().padLeft(2, '0')}';

    // Format time as HHMMSS
    final timeComponent = '${now.hour.toString().padLeft(2, '0')}'
        '${now.minute.toString().padLeft(2, '0')}'
        '${now.second.toString().padLeft(2, '0')}';

    // Generate 3 digits: combine random number with counter for uniqueness
    final baseRandom = _random.nextInt(900) + 100; // 100-999
    final uniqueComponent =
        ((baseRandom + _counter) % 1000).toString().padLeft(3, '0');

    return 'GPL$dateComponent$timeComponent$uniqueComponent';
  }

  /// Validates if a given string follows the GPL order ID format
  ///
  /// Returns true if the ID matches the expected format:
  /// - Starts with "GPL"
  /// - Followed by 8 digits (date)
  /// - Followed by 6 digits (time)
  /// - Followed by 3 digits (random)
  /// - Total length: 20 characters
  static bool isValidOrderId(String orderId) {
    if (orderId.length != 20) return false;
    if (!orderId.startsWith('GPL')) return false;

    // Check if the remaining 17 characters are all digits
    final numericPart = orderId.substring(3);
    return RegExp(r'^\d{17}$').hasMatch(numericPart);
  }

  /// Extracts the date component from a GPL order ID
  ///
  /// Returns a DateTime object representing the date when the order was created
  /// Returns null if the order ID is invalid
  static DateTime? extractDateFromOrderId(String orderId) {
    if (!isValidOrderId(orderId)) return null;

    try {
      final dateTimeString = orderId.substring(3, 17); // Extract YYYYMMDDHHMMSS
      final year = int.parse(dateTimeString.substring(0, 4));
      final month = int.parse(dateTimeString.substring(4, 6));
      final day = int.parse(dateTimeString.substring(6, 8));
      final hour = int.parse(dateTimeString.substring(8, 10));
      final minute = int.parse(dateTimeString.substring(10, 12));
      final second = int.parse(dateTimeString.substring(12, 14));

      return DateTime(year, month, day, hour, minute, second);
    } catch (e) {
      return null;
    }
  }

  /// Extracts the random component from a GPL order ID
  ///
  /// Returns the 3-digit random suffix as a string
  /// Returns null if the order ID is invalid
  static String? extractRandomComponent(String orderId) {
    if (!isValidOrderId(orderId)) return null;
    return orderId.substring(17, 20);
  }

  /// Generates a transaction ID for history entries using the GPL format
  /// This maintains consistency with the order ID format
  static String generateTransactionId() {
    return generateOrderId();
  }
}
