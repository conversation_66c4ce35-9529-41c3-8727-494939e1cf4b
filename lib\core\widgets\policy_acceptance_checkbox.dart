import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:guest_posts/core/theme/app_theme.dart';
import 'package:guest_posts/core/models/policy_model.dart';
import 'package:guest_posts/core/widgets/policy_viewer.dart';

/// A checkbox widget for accepting individual policies with links to view full text
class PolicyAcceptanceCheckbox extends StatelessWidget {
  final PolicyType policyType;
  final bool value;
  final ValueChanged<bool?> onChanged;
  final String? customText;
  final bool isRequired;

  const PolicyAcceptanceCheckbox({
    Key? key,
    required this.policyType,
    required this.value,
    required this.onChanged,
    this.customText,
    this.isRequired = true,
  }) : super(key: key);

  void _viewPolicy(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PolicyViewer(
          policyType: policyType,
          showBackButton: true,
          showAcceptButton: false,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.componentBackColor,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
        border: Border.all(
          color: value ? AppTheme.successColor : AppTheme.borderColor,
          width: value ? 2 : 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Checkbox(
            value: value,
            onChanged: onChanged,
            activeColor: AppTheme.successColor,
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RichText(
                  text: TextSpan(
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      color: AppTheme.textPrimary,
                      height: 1.4,
                    ),
                    children: [
                      TextSpan(
                        text: customText ?? 'I have read and agree to the ',
                      ),
                      WidgetSpan(
                        child: GestureDetector(
                          onTap: () => _viewPolicy(context),
                          child: Text(
                            policyType.displayName,
                            style: GoogleFonts.inter(
                              fontSize: 14,
                              color: AppTheme.primaryColor,
                              fontWeight: FontWeight.w500,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ),
                      ),
                      if (isRequired)
                        TextSpan(
                          text: ' *',
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            color: AppTheme.errorColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                    ],
                  ),
                ),
                const SizedBox(height: 4),
                GestureDetector(
                  onTap: () => _viewPolicy(context),
                  child: Text(
                    'Click here to view the full policy',
                    style: GoogleFonts.inter(
                      fontSize: 12,
                      color: AppTheme.textSecondary,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// A widget that displays multiple policy acceptance checkboxes
class PolicyAcceptanceGroup extends StatefulWidget {
  final List<PolicyType> policies;
  final Map<PolicyType, bool> acceptanceStates;
  final ValueChanged<Map<PolicyType, bool>> onChanged;
  final String? groupTitle;

  const PolicyAcceptanceGroup({
    Key? key,
    required this.policies,
    required this.acceptanceStates,
    required this.onChanged,
    this.groupTitle,
  }) : super(key: key);

  @override
  State<PolicyAcceptanceGroup> createState() => _PolicyAcceptanceGroupState();
}

class _PolicyAcceptanceGroupState extends State<PolicyAcceptanceGroup> {
  late Map<PolicyType, bool> _localStates;

  @override
  void initState() {
    super.initState();
    _localStates = Map.from(widget.acceptanceStates);
  }

  @override
  void didUpdateWidget(PolicyAcceptanceGroup oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.acceptanceStates != oldWidget.acceptanceStates) {
      _localStates = Map.from(widget.acceptanceStates);
    }
  }

  void _updateAcceptance(PolicyType policyType, bool? value) {
    setState(() {
      _localStates[policyType] = value ?? false;
    });
    widget.onChanged(_localStates);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.groupTitle != null) ...[
          Text(
            widget.groupTitle!,
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
        ],
        ...widget.policies.map((policyType) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: PolicyAcceptanceCheckbox(
              policyType: policyType,
              value: _localStates[policyType] ?? false,
              onChanged: (value) => _updateAcceptance(policyType, value),
            ),
          );
        }).toList(),
      ],
    );
  }
}

/// A simple policy link widget for footers and settings
class PolicyLink extends StatelessWidget {
  final PolicyType policyType;
  final TextStyle? textStyle;

  const PolicyLink({
    Key? key,
    required this.policyType,
    this.textStyle,
  }) : super(key: key);

  void _viewPolicy(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PolicyViewer(
          policyType: policyType,
          showBackButton: true,
          showAcceptButton: false,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _viewPolicy(context),
      child: Text(
        policyType.displayName,
        style: textStyle ??
            GoogleFonts.inter(
              fontSize: 14,
              color: AppTheme.primaryColor,
              decoration: TextDecoration.underline,
            ),
      ),
    );
  }
}

/// Validation helper for policy acceptance
class PolicyAcceptanceValidator {
  static String? validateRequired(bool? value, String policyName) {
    if (value != true) {
      return 'You must accept the $policyName to continue';
    }
    return null;
  }

  static String? validateMultiple(Map<PolicyType, bool> acceptances, List<PolicyType> required) {
    final unaccepted = required.where((policy) => acceptances[policy] != true).toList();
    
    if (unaccepted.isNotEmpty) {
      if (unaccepted.length == 1) {
        return 'You must accept the ${unaccepted.first.displayName} to continue';
      } else {
        return 'You must accept all required policies to continue';
      }
    }
    
    return null;
  }
}
