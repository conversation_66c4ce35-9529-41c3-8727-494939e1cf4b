// ignore_for_file: library_private_types_in_public_api

import 'dart:async';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:country_picker/country_picker.dart';
import 'package:guest_posts/core/services/auth_service.dart'; // Import AuthService

import 'package:guest_posts/core/utils/validators.dart';
import 'package:guest_posts/core/utils/toast_helper.dart';
import 'package:guest_posts/core/theme/app_theme.dart';
import 'package:guest_posts/core/models/policy_model.dart';
import 'package:guest_posts/core/services/policy_service.dart';
import 'package:guest_posts/core/widgets/policy_acceptance_checkbox.dart';

class SignUpScreen extends StatefulWidget {
  final AuthService authService;

  const SignUpScreen({super.key, required this.authService});

  @override
  _SignUpScreenState createState() => _SignUpScreenState();
}

class _SignUpScreenState extends State<SignUpScreen> {
  final TextEditingController emailController = TextEditingController();
  final TextEditingController usernameController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController confirmPasswordController =
      TextEditingController();
  final TextEditingController countryController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController phoneCodeController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  String? _errorMessage;
  bool _showPassword = false;
  bool _showConfirmPassword = false;
  int _signupAttempts = 0;
  DateTime? _lastSignupAttempt;
  bool _isLocked = false;
  Timer? _lockoutTimer;

  // Rate limiting constants
  static const int maxSignupAttempts = 10;
  static const Duration lockoutDuration = Duration(minutes: 15);
  static const Duration cooldownPeriod = Duration(milliseconds: 500);

  Color _currentColor = AppTheme.accentColor;
  double _leftPosition = -5;
  double _bottomPosition = -10;

  final List<Color> _colors = [
    AppTheme.accentColor,
    AppTheme.warningColor,
    AppTheme.successColor,
    AppTheme.infoColor,
    AppTheme.primaryColor,
  ];

  int _colorIndex = 0;
  late Timer _timer;

  // Policy acceptance states
  final PolicyService _policyService = PolicyService();
  Map<PolicyType, bool> _policyAcceptances = {
    PolicyType.terms: false,
    PolicyType.privacy: false,
  };

  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(const Duration(seconds: 3), (timer) {
      _changeAppearance();
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    _lockoutTimer?.cancel();
    emailController.dispose();
    usernameController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    countryController.dispose();
    phoneController.dispose();
    phoneCodeController.dispose();
    super.dispose();
  }

  void _changeAppearance() {
    if (!mounted) return;
    setState(() {
      _colorIndex = (_colorIndex + 1) % _colors.length;
      _currentColor = _colors[_colorIndex];
      _leftPosition += 10;
      _bottomPosition += 5;

      final screenWidth = MediaQuery.of(context).size.width;
      final screenHeight = MediaQuery.of(context).size.height;

      if (_leftPosition > screenWidth) {
        _leftPosition = -5;
      }
      if (_bottomPosition > screenHeight) {
        _bottomPosition = -10;
      }
    });
  }

  void _lockSignup() {
    setState(() {
      _isLocked = true;
    });
    _lockoutTimer = Timer(lockoutDuration, () {
      if (mounted) {
        setState(() {
          _isLocked = false;
          _signupAttempts = 0;
        });
      }
    });
  }

  void _showError(String message) {
    ToastHelper.showError(message);
  }

  void _signInWithGoogle() {
    setState(() => _isLoading = true);

    widget.authService.signInOrSignUpWithGoogle().then((success) {
      if (success && mounted) {
        // Navigate to home page
        GoRouter.of(context).go('/home');
      } else if (!success) {
        throw Exception('Google Sign-In failed.');
      }
    }).catchError((e) {
      if (mounted) {
        String errorMessage = 'An error occurred during Google Sign-In.';
        if (e.toString().contains('network')) {
          errorMessage = 'Network error. Please check your connection.';
        } else if (e.toString().contains('canceled')) {
          errorMessage = 'Google Sign-In was canceled.';
        }
        ToastHelper.showError(errorMessage);
      }
    }).whenComplete(() {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    });
  }

  Future<void> _signUp() async {
    if (!_formKey.currentState!.validate()) return;
    if (_isLoading) return;
    if (_isLocked) {
      _showError('Too many attempts. Please wait before trying again.');
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Check rate limiting
      if (_lastSignupAttempt != null) {
        final timeSinceLastAttempt =
            DateTime.now().difference(_lastSignupAttempt!);
        if (timeSinceLastAttempt < cooldownPeriod) {
          throw Exception('Please wait before trying again.');
        }
      }

      _lastSignupAttempt = DateTime.now();
      _signupAttempts++;

      // Validate passwords match
      if (passwordController.text != confirmPasswordController.text) {
        throw Exception('Passwords do not match.');
      }

      // Validate password strength
      if (!Validators.isValidPassword(passwordController.text)) {
        throw Exception(
            'Password must contain at least 6 characters, 1 uppercase, 1 lowercase, and 1 number.');
      }

      // Validate email format
      if (!Validators.isValidEmail(emailController.text)) {
        throw Exception('Please enter a valid email address.');
      }

      // Validate username
      if (usernameController.text.length < 3) {
        throw Exception('Username must be at least 3 characters long.');
      }

      // Validate policy acceptances
      final policyValidation = PolicyAcceptanceValidator.validateMultiple(
          _policyAcceptances, [PolicyType.terms, PolicyType.privacy]);
      if (policyValidation != null) {
        throw Exception(policyValidation);
      }

      final mobileNumber =
          phoneCodeController.text.isNotEmpty && phoneController.text.isNotEmpty
              ? '${phoneCodeController.text}${phoneController.text}'
              : null;

      final success = await widget.authService.registerWithEmail(
        name: usernameController.text.trim(),
        email: emailController.text.trim(),
        password: passwordController.text,
        mobileNumber: mobileNumber,
        isPublisher: false,
        additionalDetails: {
          'country': countryController.text.trim(),
        },
      );

      if (success) {
        _signupAttempts = 0; // Reset attempts on success

        // Record policy acceptances
        try {
          for (final policyType in [PolicyType.terms, PolicyType.privacy]) {
            await _policyService.recordPolicyAcceptance(policyType);
          }
        } catch (e) {
          print('Error recording policy acceptances: $e');
          // Don't fail registration for policy recording errors
        }

        ToastHelper.showSuccess(
            'Sign-up successful! Please verify your email.');
        if (mounted) {
          context.go('/home');
        }
      } else {
        throw Exception('Sign-up failed. Please try again.');
      }
    } catch (e) {
      String errorMessage;
      if (e.toString().contains('email-already-in-use')) {
        errorMessage = 'This email is already registered.';
      } else if (e.toString().contains('network')) {
        errorMessage = 'Network error. Please check your connection.';
      } else {
        errorMessage = e.toString().replaceFirst('Exception: ', '');
      }
      setState(() {
        _errorMessage = errorMessage;
      });
      _showError(errorMessage);

      // Handle rate limiting
      if (_signupAttempts >= maxSignupAttempts) {
        _lockSignup();
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          // Animated circular background (optimized to one container)
          Positioned(
            left: _leftPosition,
            bottom: _bottomPosition,
            child: AnimatedContainer(
              duration: const Duration(seconds: 2),
              width: 300,
              height: 300,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _currentColor.withOpacity(0.5),
              ),
            ),
          ),
          // Blurred background (reduced blur for performance)
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 50.0, sigmaY: 50.0),
            child: Container(
              color: Colors.white.withOpacity(0.7),
            ),
          ),
          // Main content
          Center(
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 20.0, vertical: 60),
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(maxWidth: 400),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Logo
                          CircleAvatar(
                            radius: 40,
                            backgroundColor: AppTheme.accentColor,
                            child: const Text(
                              'GP',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'cairo',
                              ),
                            ),
                          ),
                          const SizedBox(height: 100),
                          // Sign Up text
                          const Text(
                            'Create Your Account',
                            style: TextStyle(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                              fontFamily: 'Alatsi',
                              color: AppTheme.textPrimary,
                            ),
                          ),
                          const SizedBox(height: 30),
                          if (_errorMessage != null)
                            Container(
                              padding: const EdgeInsets.all(12),
                              margin: const EdgeInsets.only(bottom: 16),
                              decoration: BoxDecoration(
                                color: AppTheme.errorColor.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: AppTheme.errorColor),
                              ),
                              child: Row(
                                children: [
                                  Icon(Icons.error_outline,
                                      color: AppTheme.errorColor),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      _errorMessage!,
                                      style:
                                          TextStyle(color: AppTheme.errorColor),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          // Social login button (Google Sign-In)
                          SizedBox(
                            height: 50,
                            child: ElevatedButton.icon(
                              onPressed:
                                  _isLoading ? null : () => _signInWithGoogle(),
                              icon: const Icon(
                                FontAwesomeIcons.google,
                                color: AppTheme.textPrimary,
                                size: 25,
                                semanticLabel: 'Google logo',
                              ),
                              label: const Text(
                                'Sign up with Google',
                                semanticsLabel: 'Sign up with Google',
                              ),
                              style: ElevatedButton.styleFrom(
                                elevation: 0,
                                textStyle: const TextStyle(
                                    fontFamily: 'Alatsi', fontSize: 20),
                                foregroundColor: AppTheme.textPrimary,
                                backgroundColor: AppTheme.componentBackColor,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(25),
                                ),
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 32, vertical: 16),
                              ),
                            ),
                          ),
                          const SizedBox(height: 20),
                          // "OR Continue With" text
                          Row(
                            children: [
                              Expanded(
                                  child: Divider(color: AppTheme.lightGrey)),
                              const Text(
                                '  OR Continue With  ',
                                style: TextStyle(
                                  color: AppTheme.accentColor,
                                  fontSize: 20,
                                ),
                              ),
                              Expanded(
                                  child: Divider(color: AppTheme.lightGrey)),
                            ],
                          ),
                          const SizedBox(height: 20),
                          // Email field
                          SizedBox(
                            height: 50,
                            child: TextFormField(
                              controller: emailController,
                              enabled: !_isLoading,
                              keyboardType: TextInputType.emailAddress,
                              textInputAction: TextInputAction.next,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter your email';
                                }
                                if (!Validators.isValidEmail(value)) {
                                  return 'Please enter a valid email';
                                }
                                return null;
                              },
                              decoration: InputDecoration(
                                labelText: 'Email',
                                hintText: 'Enter your email',
                                filled: true,
                                fillColor: AppTheme.componentBackColor,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: BorderSide.none,
                                ),
                                prefixIcon: const Icon(Icons.email_outlined),
                                errorMaxLines: 2,
                              ),
                            ),
                          ),
                          const SizedBox(height: 10),
                          // Username field
                          SizedBox(
                            height: 50,
                            child: TextFormField(
                              controller: usernameController,
                              enabled: !_isLoading,
                              textInputAction: TextInputAction.next,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter a username';
                                }
                                if (value.length < 3) {
                                  return 'Username must be at least 3 characters';
                                }
                                return null;
                              },
                              decoration: InputDecoration(
                                labelText: 'Username',
                                hintText: 'Enter your username',
                                filled: true,
                                fillColor: AppTheme.componentBackColor,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: BorderSide.none,
                                ),
                                prefixIcon: const Icon(Icons.person_outline),
                                errorMaxLines: 2,
                              ),
                            ),
                          ),
                          const SizedBox(height: 10),
                          // Password field
                          SizedBox(
                            height: 50,
                            child: TextFormField(
                              controller: passwordController,
                              enabled: !_isLoading,
                              obscureText: !_showPassword,
                              textInputAction: TextInputAction.next,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter a password';
                                }
                                if (!Validators.isValidPassword(value)) {
                                  return 'Password must contain at least 6 characters, 1 uppercase, 1 lowercase, and 1 number';
                                }
                                return null;
                              },
                              decoration: InputDecoration(
                                labelText: 'Password',
                                hintText: 'Enter your password',
                                filled: true,
                                fillColor: AppTheme.componentBackColor,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: BorderSide.none,
                                ),
                                prefixIcon: const Icon(Icons.lock_outline),
                                suffix: TextButton(
                                  onPressed: () {
                                    setState(() {
                                      _showPassword = !_showPassword;
                                    });
                                  },
                                  child: Text(
                                    _showPassword ? 'Hide' : 'Show',
                                    style: const TextStyle(
                                        color: AppTheme.accentColor),
                                  ),
                                ),
                                errorMaxLines: 2,
                              ),
                            ),
                          ),
                          const SizedBox(height: 10),
                          // Confirm Password field
                          SizedBox(
                            height: 50,
                            child: TextFormField(
                              controller: confirmPasswordController,
                              enabled: !_isLoading,
                              obscureText: !_showConfirmPassword,
                              textInputAction: TextInputAction.next,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please confirm your password';
                                }
                                if (value != passwordController.text) {
                                  return 'Passwords do not match';
                                }
                                return null;
                              },
                              decoration: InputDecoration(
                                labelText: 'Confirm Password',
                                hintText: 'Confirm your password',
                                filled: true,
                                fillColor: AppTheme.componentBackColor,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: BorderSide.none,
                                ),
                                prefixIcon: const Icon(Icons.lock_outline),
                                suffix: TextButton(
                                  onPressed: () {
                                    setState(() {
                                      _showConfirmPassword =
                                          !_showConfirmPassword;
                                    });
                                  },
                                  child: Text(
                                    _showConfirmPassword ? 'Hide' : 'Show',
                                    style: const TextStyle(
                                        color: AppTheme.accentColor),
                                  ),
                                ),
                                errorMaxLines: 2,
                              ),
                            ),
                          ),
                          const SizedBox(height: 10),
                          // Country field
                          SizedBox(
                            height: 50,
                            child: TextFormField(
                              controller: countryController,
                              readOnly: true,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please select a country';
                                }
                                return null;
                              },
                              decoration: InputDecoration(
                                labelText: 'Country',
                                hintText: 'Select your country',
                                filled: true,
                                fillColor: AppTheme.componentBackColor,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: BorderSide.none,
                                ),
                                suffixIcon: const Icon(Icons.arrow_drop_down),
                              ),
                              onTap: () {
                                showCountryPicker(
                                  customFlagBuilder: (country) => Container(
                                    margin: const EdgeInsets.only(right: 8),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(4),
                                      child: Image.network(
                                        'https://flagcdn.com/w40/${country.countryCode.toLowerCase()}.png',
                                        width: 30,
                                        height: 20,
                                        fit: BoxFit.cover,
                                        errorBuilder:
                                            (context, error, stackTrace) =>
                                                Text(country.flagEmoji),
                                      ),
                                    ),
                                  ),
                                  useSafeArea: true,
                                  context: context,
                                  showPhoneCode: false,
                                  onSelect: (Country country) {
                                    countryController.text = country.name;
                                    phoneCodeController.text =
                                        '+${country.phoneCode}';
                                  },
                                  countryListTheme: CountryListThemeData(
                                    borderRadius: BorderRadius.circular(10),
                                    inputDecoration: InputDecoration(
                                      hintText: 'Country',
                                      filled: true,
                                      fillColor: AppTheme.componentBackColor,
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                        borderSide: BorderSide.none,
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                          const SizedBox(height: 10),
                          // Phone number with code
                          Row(
                            children: [
                              SizedBox(
                                width: 100,
                                height: 50,
                                child: TextFormField(
                                  controller: phoneCodeController,
                                  readOnly: true,
                                  decoration: InputDecoration(
                                    hintText: '+XX',
                                    filled: true,
                                    fillColor: AppTheme.componentBackColor,
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10),
                                      borderSide: BorderSide.none,
                                    ),
                                    suffixIcon:
                                        const Icon(Icons.arrow_drop_down),
                                  ),
                                  onTap: () {
                                    showCountryPicker(
                                      context: context,
                                      showPhoneCode: true,
                                      onSelect: (Country country) {
                                        phoneCodeController.text =
                                            '+${country.phoneCode}';
                                      },
                                      countryListTheme: CountryListThemeData(
                                        borderRadius: BorderRadius.circular(10),
                                        inputDecoration: InputDecoration(
                                          hintText: '+XX',
                                          filled: true,
                                          fillColor:
                                              AppTheme.componentBackColor,
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            borderSide: BorderSide.none,
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                              const SizedBox(width: 10),
                              Expanded(
                                child: SizedBox(
                                  height: 50,
                                  child: TextFormField(
                                    controller: phoneController,
                                    enabled: !_isLoading,
                                    keyboardType: TextInputType.phone,
                                    decoration: InputDecoration(
                                      labelText: 'Phone Number',
                                      hintText: 'Enter your phone number',
                                      filled: true,
                                      fillColor: AppTheme.componentBackColor,
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                        borderSide: BorderSide.none,
                                      ),
                                    ),
                                    validator: (value) {
                                      if (value != null && value.isNotEmpty) {
                                        if (!RegExp(r'^\d{7,15}$')
                                            .hasMatch(value)) {
                                          return 'Please enter a valid phone number';
                                        }
                                      }
                                      return null;
                                    },
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 20),
                          // Policy acceptance checkboxes
                          PolicyAcceptanceGroup(
                            policies: [PolicyType.terms, PolicyType.privacy],
                            acceptanceStates: _policyAcceptances,
                            onChanged: (newStates) {
                              setState(() {
                                _policyAcceptances = newStates;
                              });
                            },
                            groupTitle: 'Required Agreements',
                          ),
                          const SizedBox(height: 20),
                          // Sign Up button
                          SizedBox(
                            height: 50,
                            child: ElevatedButton(
                              onPressed: _isLoading ? null : _signUp,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppTheme.primaryColor,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(25),
                                ),
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 40, vertical: 15),
                              ),
                              child: _isLoading
                                  ? const SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                                Colors.white),
                                      ),
                                    )
                                  : const Text(
                                      'Sign Up',
                                      style: TextStyle(color: Colors.white),
                                    ),
                            ),
                          ),
                          const SizedBox(height: 20),
                          // Already have an account
                          InkWell(
                            onTap: () => context.pop(),
                            child: RichText(
                              text: TextSpan(
                                children: [
                                  const TextSpan(
                                    text: 'Already have an account? ',
                                    style: TextStyle(
                                      color: AppTheme.textPrimary,
                                      fontSize: 16,
                                    ),
                                  ),
                                  TextSpan(
                                    text: 'Log in now',
                                    style: TextStyle(
                                      color: AppTheme.accentColor,
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          // Cookie settings button (bottom-left)
          Positioned(
            bottom: 20,
            left: 20,
            child: ElevatedButton(
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Cookie Settings'),
                    content: const Text('Manage your cookie preferences here.'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('Close'),
                      ),
                    ],
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                foregroundColor: AppTheme.textPrimary,
                backgroundColor: Colors.white,
                side: BorderSide(color: AppTheme.lightGrey),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
              child: const Text('COOKIE SETTINGS'),
            ),
          ),
          // Loading overlay
          if (_isLoading)
            Stack(
              children: [
                ModalBarrier(
                    dismissible: false, color: Colors.black.withOpacity(0.3)),
                const Center(child: CircularProgressIndicator()),
              ],
            ),
        ],
      ),
    );
  }
}
