import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:guest_posts/core/theme/app_theme.dart';

/// A premium styled badge for displaying dispute status
class DisputeStatusBadge extends StatelessWidget {
  final String? disputeStatus;
  final bool isDisputed;
  final double? fontSize;
  final EdgeInsetsGeometry? padding;
  final bool showIcon;

  const DisputeStatusBadge({
    Key? key,
    this.disputeStatus,
    required this.isDisputed,
    this.fontSize,
    this.padding,
    this.showIcon = true,
  }) : super(key: key);

  Color _getDisputeColor(String? status) {
    if (!isDisputed) return Colors.transparent;
    
    switch (status?.toLowerCase()) {
      case 'open':
        return AppTheme.errorColor;
      case 'in review':
        return AppTheme.warningColor;
      case 'resolved':
        return AppTheme.successColor;
      default:
        return AppTheme.errorColor;
    }
  }

  IconData _getDisputeIcon(String? status) {
    switch (status?.toLowerCase()) {
      case 'open':
        return Icons.warning_rounded;
      case 'in review':
        return Icons.schedule_rounded;
      case 'resolved':
        return Icons.check_circle_rounded;
      default:
        return Icons.warning_rounded;
    }
  }

  String _getDisplayText(String? status) {
    if (!isDisputed) return '';
    return status ?? 'Disputed';
  }

  @override
  Widget build(BuildContext context) {
    if (!isDisputed) {
      return const SizedBox.shrink();
    }

    final color = _getDisputeColor(disputeStatus);
    final icon = _getDisputeIcon(disputeStatus);
    final text = _getDisplayText(disputeStatus);

    return Container(
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showIcon) ...[
            Icon(
              icon,
              size: (fontSize ?? 12) + 2,
              color: color,
            ),
            const SizedBox(width: 4),
          ],
          Text(
            text,
            style: GoogleFonts.inter(
              fontSize: fontSize ?? 12,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}

/// A larger dispute warning indicator for prominent display
class DisputeWarningIndicator extends StatelessWidget {
  final String? disputeStatus;
  final bool isDisputed;
  final VoidCallback? onTap;

  const DisputeWarningIndicator({
    Key? key,
    this.disputeStatus,
    required this.isDisputed,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!isDisputed) {
      return const SizedBox.shrink();
    }

    final color = disputeStatus?.toLowerCase() == 'resolved' 
        ? AppTheme.successColor 
        : AppTheme.errorColor;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusL),
          border: Border.all(
            color: color.withOpacity(0.3),
            width: 1.5,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.warning_rounded,
              size: 20,
              color: color,
            ),
            const SizedBox(width: 8),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Dispute',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                ),
                if (disputeStatus != null)
                  Text(
                    disputeStatus!,
                    style: GoogleFonts.inter(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: color.withOpacity(0.8),
                    ),
                  ),
              ],
            ),
            if (onTap != null) ...[
              const SizedBox(width: 8),
              Icon(
                Icons.arrow_forward_ios_rounded,
                size: 14,
                color: color,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
