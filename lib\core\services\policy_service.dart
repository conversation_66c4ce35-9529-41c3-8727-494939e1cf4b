import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:guest_posts/core/models/policy_model.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';

/// Service for managing policies and user acceptances
class PolicyService {
  static final PolicyService _instance = PolicyService._internal();
  factory PolicyService() => _instance;
  PolicyService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Cache for policies to avoid repeated Firestore calls
  final Map<String, PolicyModel> _policyCache = {};
  DateTime? _lastCacheUpdate;
  static const Duration _cacheExpiry = Duration(minutes: 30);

  /// Get a specific policy by type
  Future<PolicyModel?> getPolicy(PolicyType policyType) async {
    try {
      // Check cache first
      if (_policyCache.containsKey(policyType.id) && 
          _lastCacheUpdate != null &&
          DateTime.now().difference(_lastCacheUpdate!) < _cacheExpiry) {
        return _policyCache[policyType.id];
      }

      final doc = await _firestore
          .collection('policies')
          .doc(policyType.id)
          .get();

      if (doc.exists) {
        final policy = PolicyModel.fromFirestore(policyType.id, doc.data()!);
        _policyCache[policyType.id] = policy;
        _lastCacheUpdate = DateTime.now();
        return policy;
      }
      return null;
    } catch (e) {
      print('Error fetching policy ${policyType.id}: $e');
      return null;
    }
  }

  /// Get all policies
  Future<Map<PolicyType, PolicyModel>> getAllPolicies() async {
    final Map<PolicyType, PolicyModel> policies = {};
    
    for (PolicyType type in PolicyType.values) {
      final policy = await getPolicy(type);
      if (policy != null) {
        policies[type] = policy;
      }
    }
    
    return policies;
  }

  /// Generate a version hash for policy content
  String _generatePolicyVersion(String content) {
    final bytes = utf8.encode(content);
    final digest = sha256.convert(bytes);
    return digest.toString().substring(0, 16); // Use first 16 characters
  }

  /// Check if user has accepted a specific policy
  Future<bool> hasUserAcceptedPolicy(PolicyType policyType, {String? userId}) async {
    try {
      final uid = userId ?? _auth.currentUser?.uid;
      if (uid == null) return false;

      final userDoc = await _firestore.collection('users').doc(uid).get();
      if (!userDoc.exists) return false;

      final userData = userDoc.data()!;
      final acceptances = userData['policyAcceptances'] as Map<String, dynamic>?;
      
      if (acceptances == null || !acceptances.containsKey(policyType.id)) {
        return false;
      }

      // Check if the accepted version is still current
      final policy = await getPolicy(policyType);
      if (policy == null) return false;

      final currentVersion = _generatePolicyVersion(policy.content);
      final acceptedVersion = acceptances[policyType.id]['policyVersion'] as String?;
      
      return acceptedVersion == currentVersion;
    } catch (e) {
      print('Error checking policy acceptance for ${policyType.id}: $e');
      return false;
    }
  }

  /// Record user acceptance of a policy
  Future<bool> recordPolicyAcceptance(PolicyType policyType, {String? userId}) async {
    try {
      final uid = userId ?? _auth.currentUser?.uid;
      if (uid == null) return false;

      final policy = await getPolicy(policyType);
      if (policy == null) return false;

      final version = _generatePolicyVersion(policy.content);
      final acceptance = PolicyAcceptanceModel(
        policyId: policyType.id,
        acceptedAt: DateTime.now(),
        policyVersion: version,
      );

      await _firestore.collection('users').doc(uid).update({
        'policyAcceptances.${policyType.id}': acceptance.toFirestore(),
      });

      return true;
    } catch (e) {
      print('Error recording policy acceptance for ${policyType.id}: $e');
      return false;
    }
  }

  /// Get user's policy acceptances
  Future<Map<PolicyType, PolicyAcceptanceModel>> getUserPolicyAcceptances({String? userId}) async {
    try {
      final uid = userId ?? _auth.currentUser?.uid;
      if (uid == null) return {};

      final userDoc = await _firestore.collection('users').doc(uid).get();
      if (!userDoc.exists) return {};

      final userData = userDoc.data()!;
      final acceptances = userData['policyAcceptances'] as Map<String, dynamic>?;
      
      if (acceptances == null) return {};

      final Map<PolicyType, PolicyAcceptanceModel> result = {};
      
      for (final entry in acceptances.entries) {
        final policyType = PolicyType.fromId(entry.key);
        if (policyType != null) {
          result[policyType] = PolicyAcceptanceModel.fromFirestore(
            entry.value as Map<String, dynamic>
          );
        }
      }
      
      return result;
    } catch (e) {
      print('Error getting user policy acceptances: $e');
      return {};
    }
  }

  /// Check if user needs to accept policies for a specific context
  Future<List<PolicyType>> getRequiredPoliciesForContext(String context, {String? userId}) async {
    final List<PolicyType> requiredPolicies = [];
    
    // Define policy requirements for different contexts
    final Map<String, List<PolicyRequirement>> contextRequirements = {
      'registration': [
        PolicyRequirement(policyType: PolicyType.terms, isRequired: true, context: 'registration'),
        PolicyRequirement(policyType: PolicyType.privacy, isRequired: true, context: 'registration'),
      ],
      'role_conversion': [
        PolicyRequirement(policyType: PolicyType.publisherTerms, isRequired: true, context: 'role_conversion'),
      ],
      'payment': [
        PolicyRequirement(policyType: PolicyType.refund, isRequired: true, context: 'payment'),
        PolicyRequirement(policyType: PolicyType.terms, isRequired: true, context: 'payment'),
      ],
      'order_placement': [
        PolicyRequirement(policyType: PolicyType.terms, isRequired: true, context: 'order_placement'),
        PolicyRequirement(policyType: PolicyType.refund, isRequired: true, context: 'order_placement'),
      ],
    };

    final requirements = contextRequirements[context] ?? [];
    
    for (final requirement in requirements) {
      if (requirement.isRequired) {
        final hasAccepted = await hasUserAcceptedPolicy(requirement.policyType, userId: userId);
        if (!hasAccepted) {
          requiredPolicies.add(requirement.policyType);
        }
      }
    }
    
    return requiredPolicies;
  }

  /// Validate that all required policies are accepted for a context
  Future<bool> validatePolicyAcceptanceForContext(String context, {String? userId}) async {
    final requiredPolicies = await getRequiredPoliciesForContext(context, userId: userId);
    return requiredPolicies.isEmpty;
  }

  /// Clear policy cache (useful for testing or when policies are updated)
  void clearCache() {
    _policyCache.clear();
    _lastCacheUpdate = null;
  }

  /// Stream policy updates
  Stream<PolicyModel?> watchPolicy(PolicyType policyType) {
    return _firestore
        .collection('policies')
        .doc(policyType.id)
        .snapshots()
        .map((doc) {
      if (doc.exists) {
        final policy = PolicyModel.fromFirestore(policyType.id, doc.data()!);
        _policyCache[policyType.id] = policy;
        _lastCacheUpdate = DateTime.now();
        return policy;
      }
      return null;
    });
  }
}
