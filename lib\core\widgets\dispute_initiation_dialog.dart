import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:guest_posts/core/theme/app_theme.dart';
import 'package:guest_posts/core/models/order_model.dart';
import 'package:guest_posts/core/utils/toast_helper.dart';
import 'package:intl/intl.dart';

/// Dialog for initiating disputes on orders
class DisputeInitiationDialog extends StatefulWidget {
  final OrderModel order;
  final String userRole; // 'buyer' or 'publisher'
  final VoidCallback? onDisputeSubmitted;

  const DisputeInitiationDialog({
    Key? key,
    required this.order,
    required this.userRole,
    this.onDisputeSubmitted,
  }) : super(key: key);

  @override
  State<DisputeInitiationDialog> createState() => _DisputeInitiationDialogState();
}

class _DisputeInitiationDialogState extends State<DisputeInitiationDialog> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  
  String? _selectedReason;
  List<PlatformFile> _selectedFiles = [];
  bool _isSubmitting = false;

  // Dispute reasons for buyers
  final List<String> _buyerReasons = [
    'Quality Issues',
    'Content Not Published',
    'Wrong Placement',
    'Backlink Issues',
    'Timeline Violations',
    'Guideline Violations',
    'Technical Issues',
  ];

  // Dispute reasons for publishers
  final List<String> _publisherReasons = [
    'Payment Issues',
    'Content Violations',
    'Unreasonable Demands',
    'False Claims',
    'Spam Content',
  ];

  List<String> get _disputeReasons => 
      widget.userRole == 'buyer' ? _buyerReasons : _publisherReasons;

  @override
  void dispose() {
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _pickFiles() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        allowMultiple: true,
        type: FileType.custom,
        allowedExtensions: ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'],
      );

      if (result != null) {
        setState(() {
          _selectedFiles = result.files;
        });
      }
    } catch (e) {
      ToastHelper.showError('Error selecting files: $e');
    }
  }

  Future<List<String>> _uploadFiles() async {
    List<String> uploadedUrls = [];
    
    for (PlatformFile file in _selectedFiles) {
      try {
        final fileName = '${DateTime.now().millisecondsSinceEpoch}_${file.name}';
        final ref = FirebaseStorage.instance
            .ref()
            .child('dispute_evidence')
            .child(widget.order.orderId!)
            .child(fileName);

        if (file.bytes != null) {
          await ref.putData(file.bytes!);
          final downloadUrl = await ref.getDownloadURL();
          uploadedUrls.add(downloadUrl);
        }
      } catch (e) {
        print('Error uploading file ${file.name}: $e');
      }
    }
    
    return uploadedUrls;
  }

  Future<void> _submitDispute() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedReason == null) {
      ToastHelper.showError('Please select a dispute reason');
      return;
    }

    // Show confirmation dialog
    final confirmed = await _showConfirmationDialog();
    if (!confirmed) return;

    setState(() => _isSubmitting = true);

    try {
      // Upload files if any
      List<String> evidenceUrls = [];
      if (_selectedFiles.isNotEmpty) {
        evidenceUrls = await _uploadFiles();
      }

      // Update order in Firestore
      await FirebaseFirestore.instance
          .collection('orders')
          .doc(widget.order.orderId)
          .update({
        'isDisputed': true,
        'disputeStatus': 'Open',
        'disputeNote': _descriptionController.text.trim(),
        'disputeReason': _selectedReason,
        'disputeInitiatedBy': widget.userRole,
        'disputeCreatedAt': FieldValue.serverTimestamp(),
        'disputeEvidence': evidenceUrls,
        'lastUpdated': FieldValue.serverTimestamp(),
      });

      ToastHelper.showSuccess('Dispute submitted successfully');
      
      // Call callback to refresh parent
      if (widget.onDisputeSubmitted != null) {
        widget.onDisputeSubmitted!();
      }
      
      Navigator.of(context).pop();
    } catch (e) {
      ToastHelper.showError('Error submitting dispute: $e');
    } finally {
      setState(() => _isSubmitting = false);
    }
  }

  Future<bool> _showConfirmationDialog() async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusL),
        ),
        title: Text(
          'Confirm Dispute Submission',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        content: Text(
          'Are you sure you want to submit this dispute? This action will notify the platform administrators for review.',
          style: GoogleFonts.inter(
            color: AppTheme.textSecondary,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(
              'Cancel',
              style: GoogleFonts.inter(color: AppTheme.textSecondary),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.warningColor,
              foregroundColor: Colors.white,
            ),
            child: Text(
              'Submit Dispute',
              style: GoogleFonts.inter(fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    ) ?? false;
  }

  Widget _buildOrderSummary() {
    return Card(
      elevation: 0,
      color: AppTheme.componentBackColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusL),
        side: BorderSide(color: AppTheme.borderColor),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Order Summary',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimary,
              ),
            ),
            const SizedBox(height: 12),
            _buildSummaryRow('Order ID', widget.order.orderId ?? 'N/A'),
            _buildSummaryRow('Website', widget.order.websiteUrl),
            _buildSummaryRow('Status', widget.order.status),
            _buildSummaryRow('Amount', '\$${widget.order.totalPrice.toStringAsFixed(2)}'),
            _buildSummaryRow('Date', 
              DateFormat('MMM d, yyyy').format(widget.order.orderDate.toDate())),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: GoogleFonts.inter(
                fontSize: 14,
                color: AppTheme.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.inter(
                fontSize: 14,
                color: AppTheme.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusXL),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 600, maxHeight: 700),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusXL),
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Colors.white, Color(0xFFF8FAFC)],
            stops: [0.0, 1.0],
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20,
              spreadRadius: 0,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.fromLTRB(28, 28, 28, 20),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(AppTheme.borderRadiusXL),
                  topRight: Radius.circular(AppTheme.borderRadiusXL),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppTheme.warningColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppTheme.borderRadiusL),
                    ),
                    child: Icon(
                      Icons.report_problem_rounded,
                      size: 24,
                      color: AppTheme.warningColor,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Report Issue',
                          style: GoogleFonts.poppins(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textPrimary,
                          ),
                        ),
                        Text(
                          'Submit a dispute for this order',
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            color: AppTheme.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close_rounded),
                    style: IconButton.styleFrom(
                      backgroundColor: AppTheme.borderColor.withOpacity(0.5),
                      foregroundColor: AppTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.fromLTRB(28, 0, 28, 28),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Order Summary
                      _buildOrderSummary(),
                      const SizedBox(height: 24),

                      // Dispute Reason
                      Text(
                        'Dispute Reason *',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<String>(
                        value: _selectedReason,
                        decoration: InputDecoration(
                          hintText: 'Select a reason for the dispute',
                          filled: true,
                          fillColor: AppTheme.componentBackColor,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusL),
                            borderSide: BorderSide(color: AppTheme.borderColor),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusL),
                            borderSide: BorderSide(color: AppTheme.borderColor),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusL),
                            borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
                          ),
                        ),
                        items: _disputeReasons.map((reason) {
                          return DropdownMenuItem(
                            value: reason,
                            child: Text(
                              reason,
                              style: GoogleFonts.inter(
                                fontSize: 14,
                                color: AppTheme.textPrimary,
                              ),
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedReason = value;
                          });
                        },
                      ),
                      const SizedBox(height: 20),

                      // Description
                      Text(
                        'Detailed Description *',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _descriptionController,
                        maxLines: 5,
                        decoration: InputDecoration(
                          hintText: 'Please provide a detailed description of the issue (minimum 50 characters)',
                          hintStyle: GoogleFonts.inter(
                            fontSize: 14,
                            color: AppTheme.textLight,
                          ),
                          filled: true,
                          fillColor: AppTheme.componentBackColor,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusL),
                            borderSide: BorderSide(color: AppTheme.borderColor),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusL),
                            borderSide: BorderSide(color: AppTheme.borderColor),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusL),
                            borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please provide a description';
                          }
                          if (value.trim().length < 50) {
                            return 'Description must be at least 50 characters';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 20),

                      // File Upload Section
                      Text(
                        'Supporting Evidence (Optional)',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: AppTheme.componentBackColor,
                          borderRadius: BorderRadius.circular(AppTheme.borderRadiusL),
                          border: Border.all(color: AppTheme.borderColor),
                        ),
                        child: Column(
                          children: [
                            if (_selectedFiles.isEmpty) ...[
                              Icon(
                                Icons.cloud_upload_outlined,
                                size: 48,
                                color: AppTheme.textLight,
                              ),
                              const SizedBox(height: 12),
                              Text(
                                'Upload screenshots, documents, or other evidence',
                                style: GoogleFonts.inter(
                                  fontSize: 14,
                                  color: AppTheme.textSecondary,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Supported formats: JPG, PNG, PDF, DOC, DOCX',
                                style: GoogleFonts.inter(
                                  fontSize: 12,
                                  color: AppTheme.textLight,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ] else ...[
                              ...(_selectedFiles.map((file) => Padding(
                                padding: const EdgeInsets.symmetric(vertical: 4),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.attach_file,
                                      size: 16,
                                      color: AppTheme.primaryColor,
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        file.name,
                                        style: GoogleFonts.inter(
                                          fontSize: 14,
                                          color: AppTheme.textPrimary,
                                        ),
                                      ),
                                    ),
                                    IconButton(
                                      icon: Icon(
                                        Icons.close,
                                        size: 16,
                                        color: AppTheme.errorColor,
                                      ),
                                      onPressed: () {
                                        setState(() {
                                          _selectedFiles.remove(file);
                                        });
                                      },
                                    ),
                                  ],
                                ),
                              ))),
                            ],
                            const SizedBox(height: 12),
                            ElevatedButton.icon(
                              onPressed: _pickFiles,
                              icon: const Icon(Icons.add),
                              label: Text(_selectedFiles.isEmpty ? 'Choose Files' : 'Add More Files'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppTheme.primaryColor,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 32),

                      // Submit Button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _isSubmitting ? null : _submitDispute,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.warningColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(AppTheme.borderRadiusL),
                            ),
                          ),
                          child: _isSubmitting
                              ? Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Text(
                                      'Submitting Dispute...',
                                      style: GoogleFonts.inter(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                )
                              : Text(
                                  'Submit Dispute',
                                  style: GoogleFonts.inter(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
