import 'package:flutter_test/flutter_test.dart';
import 'package:guest_posts/core/utils/order_id_generator.dart';

void main() {
  group('OrderIdGenerator', () {
    test('generates valid GPL format order ID', () {
      final orderId = OrderIdGenerator.generateOrderId();
      
      // Check format: GPL + 8 digits (date) + 6 digits (time) + 3 digits (random)
      expect(orderId.length, equals(20));
      expect(orderId.startsWith('GPL'), isTrue);
      
      // Check if the remaining 17 characters are all digits
      final numericPart = orderId.substring(3);
      expect(RegExp(r'^\d{17}$').hasMatch(numericPart), isTrue);
    });

    test('validates order ID format correctly', () {
      // Valid format
      expect(OrderIdGenerator.isValidOrderId('GPL20241215143052847'), isTrue);
      
      // Invalid formats
      expect(OrderIdGenerator.isValidOrderId('GPL2024121514305284'), isFalse); // Too short
      expect(OrderIdGenerator.isValidOrderId('GPL202412151430528471'), isFalse); // Too long
      expect(OrderIdGenerator.isValidOrderId('ABC20241215143052847'), isFalse); // Wrong prefix
      expect(OrderIdGenerator.isValidOrderId('GPL2024121514305284a'), isFalse); // Contains letter
      expect(OrderIdGenerator.isValidOrderId(''), isFalse); // Empty string
    });

    test('extracts date from order ID correctly', () {
      final testOrderId = 'GPL20241215143052847';
      final extractedDate = OrderIdGenerator.extractDateFromOrderId(testOrderId);
      
      expect(extractedDate, isNotNull);
      expect(extractedDate!.year, equals(2024));
      expect(extractedDate.month, equals(12));
      expect(extractedDate.day, equals(15));
      expect(extractedDate.hour, equals(14));
      expect(extractedDate.minute, equals(30));
      expect(extractedDate.second, equals(52));
    });

    test('extracts random component from order ID correctly', () {
      final testOrderId = 'GPL20241215143052847';
      final randomComponent = OrderIdGenerator.extractRandomComponent(testOrderId);
      
      expect(randomComponent, equals('847'));
    });

    test('returns null for invalid order ID in extraction methods', () {
      const invalidOrderId = 'INVALID_ID';
      
      expect(OrderIdGenerator.extractDateFromOrderId(invalidOrderId), isNull);
      expect(OrderIdGenerator.extractRandomComponent(invalidOrderId), isNull);
    });

    test('generates unique order IDs', () {
      final orderIds = <String>{};
      
      // Generate 100 order IDs and check for uniqueness
      for (int i = 0; i < 100; i++) {
        final orderId = OrderIdGenerator.generateOrderId();
        expect(orderIds.contains(orderId), isFalse, 
            reason: 'Duplicate order ID generated: $orderId');
        orderIds.add(orderId);
      }
    });

    test('generates transaction ID with same format as order ID', () {
      final transactionId = OrderIdGenerator.generateTransactionId();
      
      // Should have same format as order ID
      expect(OrderIdGenerator.isValidOrderId(transactionId), isTrue);
    });

    test('generated order ID represents current time approximately', () {
      final beforeGeneration = DateTime.now();
      final orderId = OrderIdGenerator.generateOrderId();
      final afterGeneration = DateTime.now();
      
      final extractedDate = OrderIdGenerator.extractDateFromOrderId(orderId);
      expect(extractedDate, isNotNull);
      
      // The extracted date should be between before and after generation times
      // Allow for some tolerance (1 second) due to processing time
      expect(extractedDate!.isAfter(beforeGeneration.subtract(const Duration(seconds: 1))), isTrue);
      expect(extractedDate.isBefore(afterGeneration.add(const Duration(seconds: 1))), isTrue);
    });
  });
}
