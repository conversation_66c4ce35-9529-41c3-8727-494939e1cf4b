import 'package:cloud_firestore/cloud_firestore.dart';

/// Model for policy documents
class PolicyModel {
  final String id;
  final String content;
  final DateTime updatedAt;

  PolicyModel({
    required this.id,
    required this.content,
    required this.updatedAt,
  });

  factory PolicyModel.fromFirestore(String id, Map<String, dynamic> data) {
    return PolicyModel(
      id: id,
      content: data['content'] ?? '',
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'content': content,
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }
}

/// Model for tracking user policy acceptances
class PolicyAcceptanceModel {
  final String policyId;
  final DateTime acceptedAt;
  final String policyVersion; // Hash or timestamp of policy content when accepted

  PolicyAcceptanceModel({
    required this.policyId,
    required this.acceptedAt,
    required this.policyVersion,
  });

  factory PolicyAcceptanceModel.fromFirestore(Map<String, dynamic> data) {
    return PolicyAcceptanceModel(
      policyId: data['policyId'] ?? '',
      acceptedAt: (data['acceptedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      policyVersion: data['policyVersion'] ?? '',
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'policyId': policyId,
      'acceptedAt': Timestamp.fromDate(acceptedAt),
      'policyVersion': policyVersion,
    };
  }
}

/// Enum for policy types
enum PolicyType {
  privacy('privacy', 'Privacy Policy'),
  publisherTerms('publisher_terms', 'Publisher Terms'),
  refund('refund', 'Refund Policy'),
  terms('terms', 'Terms of Service');

  const PolicyType(this.id, this.displayName);

  final String id;
  final String displayName;

  static PolicyType? fromId(String id) {
    for (PolicyType type in PolicyType.values) {
      if (type.id == id) return type;
    }
    return null;
  }
}

/// Model for policy requirements in different contexts
class PolicyRequirement {
  final PolicyType policyType;
  final bool isRequired;
  final String context; // e.g., 'registration', 'role_conversion', 'payment'

  PolicyRequirement({
    required this.policyType,
    required this.isRequired,
    required this.context,
  });
}
