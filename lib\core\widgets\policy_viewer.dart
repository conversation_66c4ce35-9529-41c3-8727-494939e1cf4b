import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:guest_posts/core/theme/app_theme.dart';
import 'package:guest_posts/core/models/policy_model.dart';
import 'package:guest_posts/core/services/policy_service.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

/// A reusable widget for displaying policy content with markdown rendering
class PolicyViewer extends StatefulWidget {
  final PolicyType policyType;
  final bool showBackButton;
  final bool showAcceptButton;
  final VoidCallback? onAccept;
  final VoidCallback? onDecline;
  final VoidCallback? onBack;

  const PolicyViewer({
    Key? key,
    required this.policyType,
    this.showBackButton = true,
    this.showAcceptButton = false,
    this.onAccept,
    this.onDecline,
    this.onBack,
  }) : super(key: key);

  @override
  State<PolicyViewer> createState() => _PolicyViewerState();
}

class _PolicyViewerState extends State<PolicyViewer> {
  final PolicyService _policyService = PolicyService();
  PolicyModel? _policy;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadPolicy();
  }

  Future<void> _loadPolicy() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final policy = await _policyService.getPolicy(widget.policyType);
      setState(() {
        _policy = policy;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to load policy: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: _buildContent(),
          ),
          if (widget.showAcceptButton) _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (widget.showBackButton)
            IconButton(
              onPressed: widget.onBack ?? () => Navigator.of(context).pop(),
              icon: const Icon(Icons.arrow_back_rounded),
              style: IconButton.styleFrom(
                backgroundColor: AppTheme.componentBackColor,
                foregroundColor: AppTheme.textPrimary,
              ),
            ),
          if (widget.showBackButton) const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.policyType.displayName,
                  style: GoogleFonts.poppins(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
                ),
                if (_policy != null)
                  Text(
                    'Last updated: ${DateFormat('MMMM d, yyyy').format(_policy!.updatedAt)}',
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      color: AppTheme.textSecondary,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppTheme.errorColor,
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: GoogleFonts.inter(
                fontSize: 16,
                color: AppTheme.errorColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadPolicy,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_policy == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.description_outlined,
              size: 64,
              color: AppTheme.textSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              'Policy not found',
              style: GoogleFonts.inter(
                fontSize: 16,
                color: AppTheme.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(24),
      child: Card(
        elevation: 0,
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusL),
          side: BorderSide(color: AppTheme.borderColor),
        ),
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Markdown(
            data: _policy!.content,
            styleSheet: _buildMarkdownStyleSheet(),
            onTapLink: (text, href, title) {
              if (href != null) {
                _launchUrl(href);
              }
            },
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (widget.onDecline != null)
            Expanded(
              child: OutlinedButton(
                onPressed: widget.onDecline,
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppTheme.errorColor,
                  side: BorderSide(color: AppTheme.errorColor),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
                  ),
                ),
                child: Text(
                  'Decline',
                  style: GoogleFonts.inter(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          if (widget.onDecline != null) const SizedBox(width: 16),
          Expanded(
            flex: widget.onDecline != null ? 1 : 2,
            child: ElevatedButton(
              onPressed: widget.onAccept,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
                ),
              ),
              child: Text(
                'Accept',
                style: GoogleFonts.inter(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  MarkdownStyleSheet _buildMarkdownStyleSheet() {
    return MarkdownStyleSheet(
      h1: GoogleFonts.poppins(
        fontSize: 28,
        fontWeight: FontWeight.bold,
        color: AppTheme.textPrimary,
        height: 1.3,
      ),
      h2: GoogleFonts.poppins(
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: AppTheme.textPrimary,
        height: 1.3,
      ),
      h3: GoogleFonts.poppins(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: AppTheme.textPrimary,
        height: 1.3,
      ),
      h4: GoogleFonts.poppins(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: AppTheme.textPrimary,
        height: 1.3,
      ),
      h5: GoogleFonts.poppins(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: AppTheme.textPrimary,
        height: 1.3,
      ),
      h6: GoogleFonts.poppins(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        color: AppTheme.textPrimary,
        height: 1.3,
      ),
      p: GoogleFonts.inter(
        fontSize: 16,
        color: AppTheme.textPrimary,
        height: 1.6,
      ),
      listBullet: GoogleFonts.inter(
        fontSize: 16,
        color: AppTheme.textPrimary,
        height: 1.6,
      ),
      a: GoogleFonts.inter(
        fontSize: 16,
        color: AppTheme.primaryColor,
        decoration: TextDecoration.underline,
      ),
      code: GoogleFonts.sourceCodePro(
        fontSize: 14,
        color: AppTheme.textPrimary,
        backgroundColor: AppTheme.componentBackColor,
      ),
      blockquote: GoogleFonts.inter(
        fontSize: 16,
        color: AppTheme.textSecondary,
        fontStyle: FontStyle.italic,
      ),
    );
  }

  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      // Handle error silently or show a toast
      print('Error launching URL: $e');
    }
  }
}
