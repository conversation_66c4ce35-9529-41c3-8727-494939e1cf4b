import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:guest_posts/core/theme/app_theme.dart';
import 'package:guest_posts/core/models/order_model.dart';
import 'package:guest_posts/core/widgets/dispute_status_badge.dart';
import 'package:intl/intl.dart';

/// A premium dialog for displaying dispute details
class DisputeDetailsDialog extends StatelessWidget {
  final OrderModel order;
  final String userRole; // 'buyer' or 'publisher'

  const DisputeDetailsDialog({
    Key? key,
    required this.order,
    required this.userRole,
  }) : super(key: key);

  Color _getDisputeColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'open':
        return AppTheme.errorColor;
      case 'in review':
        return AppTheme.warningColor;
      case 'resolved':
        return AppTheme.successColor;
      default:
        return AppTheme.errorColor;
    }
  }

  IconData _getDisputeIcon(String? status) {
    switch (status?.toLowerCase()) {
      case 'open':
        return Icons.warning_rounded;
      case 'in review':
        return Icons.schedule_rounded;
      case 'resolved':
        return Icons.check_circle_rounded;
      default:
        return Icons.warning_rounded;
    }
  }

  Widget _buildInfoRow(String label, String value, {IconData? icon, Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (icon != null) ...[
            Icon(icon, size: 18, color: AppTheme.textSecondary),
            const SizedBox(width: 8),
          ],
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppTheme.textSecondary,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: SelectableText(
              value,
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: valueColor ?? AppTheme.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title, {IconData? icon}) {
    return Padding(
      padding: const EdgeInsets.only(top: 20, bottom: 12),
      child: Row(
        children: [
          if (icon != null) ...[
            Icon(icon, size: 20, color: AppTheme.primaryColor),
            const SizedBox(width: 8),
          ],
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final disputeColor = _getDisputeColor(order.disputeStatus);
    final disputeIcon = _getDisputeIcon(order.disputeStatus);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusXL),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 700, maxHeight: 600),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusXL),
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Colors.white, Color(0xFFF8FAFC)],
            stops: [0.0, 1.0],
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20,
              spreadRadius: 0,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.fromLTRB(28, 28, 28, 20),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(AppTheme.borderRadiusXL),
                  topRight: Radius.circular(AppTheme.borderRadiusXL),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: disputeColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppTheme.borderRadiusL),
                    ),
                    child: Icon(
                      disputeIcon,
                      size: 24,
                      color: disputeColor,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Dispute Details',
                          style: GoogleFonts.poppins(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 4),
                        DisputeStatusBadge(
                          disputeStatus: order.disputeStatus,
                          isDisputed: order.isDisputed ?? false,
                          fontSize: 14,
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close_rounded),
                    style: IconButton.styleFrom(
                      backgroundColor: AppTheme.borderColor.withOpacity(0.5),
                      foregroundColor: AppTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),

            // Divider
            Container(
              height: 1,
              margin: const EdgeInsets.symmetric(horizontal: 28),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.transparent,
                    AppTheme.borderColor,
                    AppTheme.borderColor,
                    Colors.transparent,
                  ],
                  stops: const [0.0, 0.2, 0.8, 1.0],
                ),
              ),
            ),

            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.fromLTRB(28, 20, 28, 28),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Order Information
                    _buildSectionTitle('Order Information', icon: Icons.receipt_long_rounded),
                    Card(
                      elevation: 0,
                      color: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppTheme.borderRadiusL),
                        side: BorderSide(color: AppTheme.borderColor),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          children: [
                            _buildInfoRow('Order ID', order.orderId ?? 'N/A', icon: Icons.tag_rounded),
                            _buildInfoRow('Website', order.websiteUrl, icon: Icons.language_rounded),
                            _buildInfoRow('Order Date', 
                              DateFormat('MMMM d, yyyy').format(order.orderDate.toDate()),
                              icon: Icons.calendar_today_rounded),
                            _buildInfoRow('Status', order.status, 
                              icon: Icons.info_outline_rounded,
                              valueColor: _getStatusColor(order.status)),
                          ],
                        ),
                      ),
                    ),

                    // Dispute Information
                    _buildSectionTitle('Dispute Information', icon: Icons.gavel_rounded),
                    Card(
                      elevation: 0,
                      color: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppTheme.borderRadiusL),
                        side: BorderSide(color: AppTheme.borderColor),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildInfoRow('Dispute Status', order.disputeStatus ?? 'Open',
                              icon: Icons.flag_rounded,
                              valueColor: disputeColor),
                            if (order.disputeNote != null && order.disputeNote!.isNotEmpty) ...[
                              const SizedBox(height: 16),
                              Text(
                                'Dispute Note:',
                                style: GoogleFonts.inter(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: AppTheme.textSecondary,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Container(
                                width: double.infinity,
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: AppTheme.componentBackColor,
                                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
                                  border: Border.all(color: AppTheme.borderColor),
                                ),
                                child: SelectableText(
                                  order.disputeNote!,
                                  style: GoogleFonts.inter(
                                    fontSize: 14,
                                    color: AppTheme.textPrimary,
                                    height: 1.5,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),

                    // Role-specific information
                    if (userRole == 'buyer') ...[
                      _buildSectionTitle('Publisher Information', icon: Icons.person_rounded),
                      Card(
                        elevation: 0,
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(AppTheme.borderRadiusL),
                          side: BorderSide(color: AppTheme.borderColor),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            children: [
                              _buildInfoRow('Publisher ID', order.publisherId, icon: Icons.person_outline_rounded),
                              _buildInfoRow('Website Domain', order.websiteDomainName, icon: Icons.domain_rounded),
                            ],
                          ),
                        ),
                      ),
                    ] else ...[
                      _buildSectionTitle('Buyer Information', icon: Icons.person_rounded),
                      Card(
                        elevation: 0,
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(AppTheme.borderRadiusL),
                          side: BorderSide(color: AppTheme.borderColor),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            children: [
                              _buildInfoRow('Buyer ID', order.buyerId, icon: Icons.person_outline_rounded),
                              _buildInfoRow('Total Amount', '\$${order.totalPrice.toStringAsFixed(2)}', 
                                icon: Icons.attach_money_rounded),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return AppTheme.successColor;
      case 'approved':
        return AppTheme.infoColor;
      case 'in progress':
        return AppTheme.warningColor;
      case 'declined':
      case 'cancelled':
        return AppTheme.errorColor;
      default:
        return AppTheme.textSecondary;
    }
  }
}
