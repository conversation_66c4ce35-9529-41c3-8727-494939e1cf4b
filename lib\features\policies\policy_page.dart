import 'package:flutter/material.dart';
import 'package:guest_posts/core/models/policy_model.dart';
import 'package:guest_posts/core/widgets/policy_viewer.dart';

/// A page for displaying individual policy documents
class PolicyPage extends StatelessWidget {
  final PolicyType policyType;

  const PolicyPage({
    Key? key,
    required this.policyType,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PolicyViewer(
      policyType: policyType,
      showBackButton: true,
      showAcceptButton: false,
    );
  }
}
