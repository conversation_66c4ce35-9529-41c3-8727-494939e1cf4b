import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:guest_posts/core/models/order_model.dart';
import 'package:guest_posts/core/utils/colors.dart';
import 'package:guest_posts/core/utils/toast_helper.dart';
import 'package:guest_posts/features/chat/chat_dialog.dart';
import 'package:guest_posts/features/publisher_acc/home/<USER>/widgets/live_url_dialog.dart';
import 'package:intl/intl.dart';
import 'package:guest_posts/core/theme/app_theme.dart';

class OrdersAndRequestsPage extends StatefulWidget {
  const OrdersAndRequestsPage({super.key});

  @override
  State<OrdersAndRequestsPage> createState() => _OrdersAndRequestsPageState();
}

class _OrdersAndRequestsPageState extends State<OrdersAndRequestsPage> {
  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return SingleChildScrollView(
      child: Container(
        color: AppTheme.backgroundColor,
        padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   children: [
            //     Text(
            //       'Orders and Requests',
            //       style: TextStyle(
            //         fontFamily: 'Space',
            //         fontSize: isSmallScreen ? 24 : 30,
            //         fontWeight: FontWeight.w700,
            //         color: Colors.black87,
            //       ),
            //     ),
            //     //  if (!isSmallScreen) _buildDateFilter(),
            //   ],
            // ),
            // const SizedBox(height: 24),
            const RequestsTableWidget(), // Requests table first

            const SizedBox(height: 24),

            Column(
              children: [
                const OrdersOverviewWidget(),

                const SizedBox(height: 24),
                const OrdersTableWidget(), // Orders table below
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget _buildDateFilter() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.amber.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.amber.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          const Icon(Icons.calendar_today, size: 20, color: Colors.black54),
          const SizedBox(width: 8),
          const Text(
            'Last 30 Days',
            style: TextStyle(
                fontFamily: 'Cairo', fontSize: 16, color: Colors.black87),
          ),
          const SizedBox(width: 8),
          const Icon(Icons.arrow_drop_down, color: Colors.black54),
        ],
      ),
    );
  }
}

class OrdersOverviewWidget extends StatefulWidget {
  const OrdersOverviewWidget({super.key});

  @override
  State<OrdersOverviewWidget> createState() => _OrdersOverviewWidgetState();
}

class _OrdersOverviewWidgetState extends State<OrdersOverviewWidget> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  int _pendingCount = 0;
  int _completedCount = 0;
  int _declinedCount = 0;
  bool _isLoading = true;
  int _approvedCount = 0;
  int _inProgressCount = 0;

  @override
  void initState() {
    super.initState();
    _loadOrderStats();
  }

  void _loadOrderStats() {
    final user = _auth.currentUser;
    if (user == null) {
      setState(() {
        _isLoading = false;
      });
      return;
    }

    _firestore
        .collection('orders')
        .where('publisherId', isEqualTo: user.uid)
        .where('orderDate',
            isGreaterThanOrEqualTo: Timestamp.fromDate(
                DateTime.now().subtract(const Duration(days: 30))))
        .snapshots()
        .listen((snapshot) {
      int pending = 0,
          completed = 0,
          declined = 0,
          approved = 0,
          inProgress = 0;
      if (snapshot.docs.isNotEmpty) {
        for (var doc in snapshot.docs) {
          final status = doc['status'] as String?;
          if (status == 'Pending') pending++;
          if (status == 'Completed') completed++;
          if (status == 'Declined') declined++;
          if (status == 'Approved') approved++;
          if (status == 'In Progress') inProgress++;
        }
      }

      if (mounted) {
        setState(() {
          _pendingCount = pending;
          _completedCount = completed;
          _declinedCount = declined;
          _approvedCount = approved;
          _inProgressCount = inProgress;
          _isLoading = false;
        });
      }
    }, onError: (e) {
      if (mounted) {
        setState(() => _isLoading = false);
      }
      print('Error loading stats: $e');
      ToastHelper.showError('Error loading stats: $e');
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;
    final isMediumScreen = screenWidth >= 600 && screenWidth < 1200;

    return _isLoading
        ? const Center(child: CircularProgressIndicator())
        : GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: isSmallScreen ? 1 : (isMediumScreen ? 2 : 4),
            mainAxisSpacing: 16,
            crossAxisSpacing: 16,
            childAspectRatio: 2,
            children: [
              _buildMetricCard(
                title: 'Pending Requests',
                value: _pendingCount.toString(),
                icon: FontAwesomeIcons.clock,
                color: AppTheme.accentColor,
                subtitle: 'Count of pending requests',
              ),
              _buildMetricCard(
                title: 'Approved Orders',
                value: _approvedCount.toString(),
                icon: FontAwesomeIcons.thumbsUp,
                color: AppTheme.accentColor,
                subtitle: 'All time',
              ),
              _buildMetricCard(
                title: 'In Progress Orders',
                value: _inProgressCount.toString(),
                icon: FontAwesomeIcons.spinner,
                color: AppTheme.accentColor,
                subtitle: 'Currently in progress',
              ),
              _buildMetricCard(
                title: 'Completed Orders',
                value: _completedCount.toString(),
                icon: FontAwesomeIcons.checkCircle,
                color: AppTheme.accentColor,
                subtitle: 'Count of completed orders',
              ),
              _buildMetricCard(
                title: 'Declined Orders',
                value: _declinedCount.toString(),
                icon: FontAwesomeIcons.timesCircle,
                color: AppTheme.accentColor,
                subtitle: 'Count of declined requests',
              ),
            ],
          );
  }

  Widget _buildMetricCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required String subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        // boxShadow: [
        //   BoxShadow(
        //       color: color.withOpacity(0.2),
        //       blurRadius: 8,
        //       offset: const Offset(0, 4)),
        // ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Icon(
            icon,
            color: AppTheme.accentColor,
          ),
          const SizedBox(height: 8),
          Text(title,
              style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87)),
          const Spacer(),
          Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(value,
                style: GoogleFonts.poppins(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color.fromARGB(255, 31, 21, 48),
                )),
            const SizedBox(height: 4),
            Text(subtitle,
                style: GoogleFonts.poppins(
                    fontSize: 12, color: Colors.black.withOpacity(0.6))),
            const SizedBox(height: 8),
          ]),
        ],
      ),
    );
  }
}

class RequestsTableWidget extends StatefulWidget {
  const RequestsTableWidget({super.key});

  @override
  State<RequestsTableWidget> createState() => _RequestsTableWidgetState();
}

class _RequestsTableWidgetState extends State<RequestsTableWidget> {
  final TextEditingController _searchController = TextEditingController();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  List<OrderModel> _requests = [];
  bool _isLoading = true;
  bool _isRequestsExpanded = false;

  @override
  void initState() {
    super.initState();
    _loadRequests();
  }

  void _loadRequests() {
    final user = _auth.currentUser;
    if (user == null) return;

    _firestore
        .collection('orders')
        .where('publisherId', isEqualTo: user.uid)
        .where('status', isEqualTo: 'Pending')
        .orderBy('orderDate', descending: true)
        .snapshots()
        .listen((snapshot) {
      if (mounted) {
        setState(() {
          _requests = snapshot.docs.map((doc) {
            final data = doc.data();
            data['orderId'] = doc.id;
            return OrderModel.fromMap(data);
          }).toList();
          _isLoading = false;
        });
      }
    }, onError: (e) {
      if (mounted) setState(() => _isLoading = false);
      print('Error loading requests: $e');
      ToastHelper.showError('Error loading requests: $e');
    });
  }

  List<OrderModel> get _searchedRequests {
    if (_searchController.text.isEmpty) return _requests;
    final searchTerm = _searchController.text.toLowerCase();
    return _requests.where((request) {
      return (request.orderId ?? '').toLowerCase().contains(searchTerm) ||
          request.websiteUrl.toLowerCase().contains(searchTerm) ||
          request.postTitle.toLowerCase().contains(searchTerm);
    }).toList();
  }

  Future<void> _updateRequestStatus(String orderId, String newStatus,
      {String? rejectionReason}) async {
    try {
      final updateData = <String, dynamic>{
        'status': newStatus,
        'updatedAt': FieldValue.serverTimestamp(),
        'actionBy': 'publisher',
        'actionTimestamp': FieldValue.serverTimestamp(),
      };
      if (newStatus == 'Approved') {
        updateData['approvalDate'] = FieldValue.serverTimestamp();
      } else if (newStatus == 'Declined' && rejectionReason != null) {
        updateData['rejectionReason'] = rejectionReason;
      }

      await _firestore.collection('orders').doc(orderId).update(updateData);
      if (!mounted) return;
      ToastHelper.showSuccess('Request $newStatus successfully');
    } catch (e) {
      print('Error updating request: $e');
      if (!mounted) return;
      ToastHelper.showError('Error updating request: $e');
    }
  }

  void _showDeclineDialog(String orderId) {
    final TextEditingController reasonController = TextEditingController();
    showDialog(
      context: context,
      builder: (context) => Center(
        child: Material(
          color: Colors.transparent,
          child: Container(
            width: 425,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(22),
              gradient: LinearGradient(
                colors: [Colors.white, Colors.white],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.red.withOpacity(0.09),
                  blurRadius: 16,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.warning_rounded, color: Colors.red, size: 38),
                const SizedBox(height: 12),
                const Text(
                  'Decline Request',
                  style: TextStyle(
                    fontSize: 21,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: reasonController,
                  decoration: InputDecoration(
                    hintText: 'Enter reason for declining',
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none),
                    filled: true,
                    fillColor: Colors.grey[100],
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: 18),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text(
                        'Cancel',
                        style: TextStyle(
                            color: Colors.grey, fontWeight: FontWeight.bold),
                      ),
                    ),
                    const SizedBox(width: 10),
                    ElevatedButton.icon(
                      onPressed: () {
                        if (reasonController.text.isNotEmpty) {
                          _updateRequestStatus(orderId, 'Declined',
                              rejectionReason: reasonController.text);
                          Navigator.pop(context);
                        }
                      },
                      icon: const Icon(Icons.close_rounded),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        elevation: 2,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      label: const Text('Decline',
                          style: TextStyle(fontWeight: FontWeight.bold)),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showRequestDetails(OrderModel request) {
    bool isMarkdownPreview = true;
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusXL)),
        elevation: 0, // Remove default elevation
        backgroundColor:
            Colors.transparent, // Transparent background for custom container
        insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
        child: Container(
          constraints: const BoxConstraints(maxWidth: 800, maxHeight: 700),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusXL),
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Colors.white, Color(0xFFF8FAFC)],
              stops: [0.0, 1.0],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                spreadRadius: 0,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: StatefulBuilder(
            builder: (context, setState) => Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Premium Header with Request ID and Status Badge
                Container(
                  padding: const EdgeInsets.fromLTRB(28, 28, 28, 20),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(AppTheme.borderRadiusXL),
                      topRight: Radius.circular(AppTheme.borderRadiusXL),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Flexible(
                            child: SelectableText(
                              'Request #${request.orderId}',
                              style: GoogleFonts.poppins(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.textPrimary,
                                letterSpacing: -0.3,
                              ),
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            decoration: BoxDecoration(
                              color: Colors.amber.withOpacity(0.1),
                              borderRadius:
                                  BorderRadius.circular(AppTheme.borderRadiusL),
                              border: Border.all(
                                color: Colors.amber.withOpacity(0.2),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.pending_rounded,
                                    size: 18, color: Colors.amber[700]),
                                const SizedBox(width: 8),
                                Text(
                                  'Pending',
                                  style: GoogleFonts.poppins(
                                    fontWeight: FontWeight.w600,
                                    fontSize: 14,
                                    color: Colors.amber[700],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Requested on ${DateFormat('MMMM d, yyyy').format(request.orderDate.toDate())}',
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          color: AppTheme.textSecondary,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),

                // Premium divider with gradient
                Container(
                  height: 1,
                  margin: const EdgeInsets.symmetric(horizontal: 28),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.transparent,
                        AppTheme.borderColor,
                        AppTheme.borderColor,
                        Colors.transparent,
                      ],
                      stops: const [0.0, 0.2, 0.8, 1.0],
                    ),
                  ),
                ),

                // Markdown toggle
                Padding(
                  padding: const EdgeInsets.fromLTRB(28, 16, 28, 0),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius:
                          BorderRadius.circular(AppTheme.borderRadiusL),
                      border: Border.all(color: AppTheme.borderColor),
                    ),
                    child: SwitchListTile(
                      title: Text(
                        'View as Markdown',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      value: isMarkdownPreview,
                      activeColor: AppTheme.primaryColor,
                      onChanged: (bool value) {
                        setState(() => isMarkdownPreview = value);
                      },
                    ),
                  ),
                ),

                // Content in a scrollable container with improved styling
                Flexible(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.fromLTRB(28, 20, 28, 0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Website and Content Section
                        _buildSectionTitle('Content Details'),
                        Card(
                          elevation: 0,
                          color: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(AppTheme.borderRadiusL),
                            side: BorderSide(color: AppTheme.borderColor),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(20),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildDetailItem(
                                  'Title:',
                                  request.postTitle,
                                  Icons.title,
                                  AppTheme.secondaryColor,
                                  isMarkdownPreview,
                                ),
                                _buildDetailItem(
                                  'Content:',
                                  request.postContent,
                                  Icons.article,
                                  AppTheme.accentColor,
                                  isMarkdownPreview,
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 20),

                        // Links and Backlink Section
                        _buildSectionTitle('Links & Backlink Information'),
                        Card(
                          elevation: 0,
                          color: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(AppTheme.borderRadiusL),
                            side: BorderSide(color: AppTheme.borderColor),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(20),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (request.links.isNotEmpty)
                                  _buildDetailItem(
                                    'Links:',
                                    request.links.join(', '),
                                    Icons.link,
                                    AppTheme.infoColor,
                                    isMarkdownPreview,
                                  ),
                                _buildDetailItem(
                                  'Backlink Type:',
                                  request.backlinkType,
                                  Icons.link_rounded,
                                  AppTheme.accentColor,
                                  isMarkdownPreview,
                                ),
                                _buildDetailItem(
                                  'Sponsored:',
                                  request.isSponsored ? 'Yes' : 'No',
                                  Icons.verified,
                                  AppTheme.successColor,
                                  isMarkdownPreview,
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 20),

                        // Payment Information Section
                        _buildSectionTitle('Payment Information'),
                        Card(
                          elevation: 0,
                          margin: EdgeInsets.zero,
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(AppTheme.borderRadiusL),
                            side: BorderSide(
                                color: AppTheme.accentColor.withOpacity(0.3)),
                          ),
                          child: Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              borderRadius:
                                  BorderRadius.circular(AppTheme.borderRadiusL),
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  AppTheme.accentColor.withOpacity(0.05),
                                  AppTheme.accentColor.withOpacity(0.02),
                                ],
                              ),
                            ),
                            child: _buildDetailItem(
                              'Total Price:',
                              '\$${request.totalPrice.toStringAsFixed(2)}',
                              Icons.attach_money,
                              AppTheme.accentColor,
                              isMarkdownPreview,
                              valueStyle: GoogleFonts.poppins(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.textPrimary,
                              ),
                            ),
                          ),
                        ),

                        // Notes Section
                        if (request.notes != null &&
                            request.notes!.isNotEmpty) ...[
                          const SizedBox(height: 20),
                          _buildSectionTitle('Additional Notes'),
                          Card(
                            elevation: 0,
                            color: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius:
                                  BorderRadius.circular(AppTheme.borderRadiusL),
                              side: BorderSide(color: AppTheme.borderColor),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(20),
                              child: _buildDetailItem(
                                'Notes:',
                                request.notes ?? 'None',
                                Icons.note,
                                AppTheme.textSecondary,
                                isMarkdownPreview,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),

                // Action buttons with premium styling
                Container(
                  padding: const EdgeInsets.fromLTRB(28, 20, 28, 28),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(AppTheme.borderRadiusXL),
                      bottomRight: Radius.circular(AppTheme.borderRadiusXL),
                    ),
                    color: Colors.white.withOpacity(0.7),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      ElevatedButton.icon(
                        icon: const Icon(Icons.check_circle_outline, size: 18),
                        label: const Text('Approve'),
                        style: ElevatedButton.styleFrom(
                          foregroundColor: Colors.white,
                          backgroundColor: AppTheme.successColor,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 12),
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(AppTheme.borderRadiusM),
                          ),
                          textStyle: GoogleFonts.poppins(
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                        ),
                        onPressed: () {
                          _updateRequestStatus(request.orderId!, 'Approved');
                          Navigator.pop(context);
                        },
                      ),
                      const SizedBox(width: 16),
                      OutlinedButton.icon(
                        icon: const Icon(Icons.cancel, size: 18),
                        label: const Text('Decline'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppTheme.errorColor,
                          side: BorderSide(color: AppTheme.errorColor),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(AppTheme.borderRadiusM),
                          ),
                          textStyle: GoogleFonts.poppins(
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                        ),
                        onPressed: () {
                          Navigator.pop(context);
                          _showDeclineDialog(request.orderId!);
                        },
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton.icon(
                        icon: const Icon(Icons.chat_bubble_outline, size: 18),
                        label: const Text('Chat with Buyer'),
                        style: ElevatedButton.styleFrom(
                          foregroundColor: Colors.white,
                          backgroundColor: AppTheme.primaryColor,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 12),
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(AppTheme.borderRadiusM),
                          ),
                          textStyle: GoogleFonts.poppins(
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                        ),
                        onPressed: () {
                          Navigator.pop(context);
                          showDialog(
                            context: context,
                            builder: (_) => ChatDialog(
                              orderId: request.orderId!,
                              buyerId: request.buyerId,
                              publisherId: request.publisherId,
                              orderTitle: request.websiteDomainName,
                            ),
                          );
                        },
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton.icon(
                        icon: const Icon(Icons.close, size: 18),
                        label: const Text('Close'),
                        style: ElevatedButton.styleFrom(
                          foregroundColor: AppTheme.textPrimary,
                          backgroundColor: Colors.grey.shade100,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 12),
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(AppTheme.borderRadiusM),
                          ),
                          textStyle: GoogleFonts.poppins(
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                        ),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Helper method to build section titles
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 20,
            decoration: BoxDecoration(
              color: AppTheme.accentColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimary,
              letterSpacing: -0.2,
            ),
          ),
        ],
      ),
    );
  }

  // Enhanced detail item with premium styling
  Widget _buildDetailItem(String label, String value, IconData icon,
      Color color, bool isMarkdownPreview,
      {TextStyle? valueStyle}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(icon, size: 16, color: color),
              ),
              const SizedBox(width: 10),
              Text(
                label,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: color,
                  letterSpacing: 0.1,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
              border: Border.all(
                color: AppTheme.borderColor,
                width: 1,
              ),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: isMarkdownPreview
                      ? MarkdownBody(
                          data: value,
                          selectable: true,
                          styleSheet: MarkdownStyleSheet(
                            p: valueStyle ??
                                GoogleFonts.inter(
                                  fontSize: 14,
                                  color: AppTheme.textPrimary,
                                  height: 1.5,
                                ),
                            h1: GoogleFonts.poppins(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.textPrimary,
                            ),
                            h2: GoogleFonts.poppins(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.textPrimary,
                            ),
                            h3: GoogleFonts.poppins(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.textPrimary,
                            ),
                            blockquote: GoogleFonts.inter(
                              fontSize: 14,
                              color: AppTheme.textSecondary,
                              fontStyle: FontStyle.italic,
                            ),
                            code: GoogleFonts.sourceCodePro(
                              fontSize: 14,
                              color: AppTheme.accentColor,
                              backgroundColor:
                                  AppTheme.accentColor.withOpacity(0.05),
                            ),
                          ),
                        )
                      : Text(
                          value,
                          style: valueStyle ??
                              GoogleFonts.inter(
                                fontSize: 14,
                                color: AppTheme.textPrimary,
                                height: 1.5,
                              ),
                        ),
                ),
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(20),
                    onTap: () {
                      Clipboard.setData(ClipboardData(text: value));
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            '$label copied to clipboard',
                            style: GoogleFonts.inter(),
                          ),
                          backgroundColor: AppTheme.accentColor,
                          behavior: SnackBarBehavior.floating,
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(AppTheme.borderRadiusM),
                          ),
                        ),
                      );
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(4.0),
                      child: Tooltip(
                        message: 'Copy to clipboard',
                        child: Icon(
                          Icons.copy_rounded,
                          size: 18,
                          color: AppTheme.textSecondary.withOpacity(0.7),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Legacy method - kept for backward compatibility
  Widget _buildDetailRow(String label, String value, bool isMarkdownPreview) {
    return _buildDetailItem(
      label,
      value,
      Icons.info_outline,
      AppTheme.textSecondary,
      isMarkdownPreview,
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  // --- Modern Premium Build ---
  @override
  Widget build(BuildContext context) {
    final isWideScreen = MediaQuery.of(context).size.width > 800;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 600),
      constraints: const BoxConstraints(minHeight: 80),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(18),
        gradient: LinearGradient(
          colors: [Colors.white, Colors.white],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
              color: Colors.blueGrey.withOpacity(0.09),
              blurRadius: 18,
              offset: const Offset(0, 8)),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header row with expand/collapse functionality
          MouseRegion(
            cursor: SystemMouseCursors.click,
            onEnter: (_) {
              setState(() {
                // You can add hover state here if needed
              });
            },
            onExit: (_) {
              setState(() {
                // Reset hover state here if needed
              });
            },
            child: InkWell(
              onTap: () {
                setState(() {
                  _isRequestsExpanded = !_isRequestsExpanded;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  children: [
                    Icon(Icons.pending_actions_rounded,
                        color: AppTheme.accentColor, size: 30),
                    const SizedBox(width: 10),
                    Text(
                      'Pending Requests',
                      style: TextStyle(
                        fontFamily: 'Space',
                        fontSize: isWideScreen ? 26 : 22,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                        letterSpacing: 0.2,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppTheme.accentColor.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${_searchedRequests.length}',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: AppTheme.accentColor,
                        ),
                      ),
                    ),
                    const Spacer(),
                    Icon(
                      _isRequestsExpanded
                          ? Icons.expand_less
                          : Icons.expand_more,
                      color: Colors.grey[600],
                      size: 28,
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Expandable content
          if (_isRequestsExpanded) ...[
            const SizedBox(height: 14),
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search by ID, website, or title',
                hintStyle: const TextStyle(
                    fontFamily: 'Cairo', fontSize: 16, color: Colors.grey),
                fillColor: Colors.grey[100],
                filled: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide.none,
                ),
                prefixIcon: const Icon(Icons.search, color: Colors.grey),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 11),
              ),
              onChanged: (value) {
                setState(() {});
              },
            ),
            const SizedBox(height: 20),
            if (_isLoading)
              const SizedBox(
                height: 200,
                child: Center(child: CircularProgressIndicator()),
              )
            else if (_searchedRequests.isEmpty)
              SizedBox(
                height: 200,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'No pending requests found',
                        style: GoogleFonts.poppins(
                            fontSize: 18, color: Colors.grey.shade600),
                      ),
                    ],
                  ),
                ),
              )
            else
              SizedBox(
                height: 200,
                child: ListView.separated(
                  itemCount: _searchedRequests.length,
                  itemBuilder: (context, index) {
                    final request = _searchedRequests[index];
                    return RequestPremiumCard(
                      request: request,
                      onView: () => _showRequestDetails(request),
                      onApprove: () =>
                          _updateRequestStatus(request.orderId!, 'Approved'),
                      onDecline: () => _showDeclineDialog(request.orderId!),
                      onChat: () => showDialog(
                        context: context,
                        builder: (_) => ChatDialog(
                          orderId: request.orderId!,
                          buyerId: request.buyerId,
                          publisherId: request.publisherId,
                          orderTitle: request.websiteDomainName,
                        ),
                      ),
                    );
                  },
                  separatorBuilder: (c, i) => const SizedBox(height: 14),
                ),
              ),
          ],
        ],
      ),
    );
  }
}

///
/// Premium Card Widget for Each Request
///
class RequestPremiumCard extends StatelessWidget {
  final OrderModel request;
  final VoidCallback onView;
  final VoidCallback onApprove;
  final VoidCallback onDecline;
  final VoidCallback onChat;

  const RequestPremiumCard({
    Key? key,
    required this.request,
    required this.onView,
    required this.onApprove,
    required this.onDecline,
    required this.onChat,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
      borderRadius: BorderRadius.circular(18),
      elevation: 0,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(18),
          color: AppTheme.componentBackColor,

          // boxShadow: [
          //   BoxShadow(
          //     color: Colors.blueGrey.withOpacity(0.10),
          //     blurRadius: 8,
          //     offset: const Offset(0, 4),
          //   ),
          // ],
        ),
        padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 19),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Status circle/icon
            CircleAvatar(
              backgroundColor: Colors.amber.withOpacity(0.12),
              radius: 27,
              child: Icon(Icons.pending_rounded,
                  size: 32, color: Colors.amber[700]),
            ),
            const SizedBox(width: 18),
            // Main Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    request.websiteUrl,
                    style: const TextStyle(
                        fontFamily: 'Space',
                        fontWeight: FontWeight.bold,
                        fontSize: 17,
                        color: Colors.black87,
                        overflow: TextOverflow.ellipsis),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    request.postTitle,
                    style: TextStyle(
                        fontFamily: 'Cairo',
                        fontWeight: FontWeight.w500,
                        fontSize: 14.5,
                        color: Colors.grey[700]),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    crossAxisAlignment: WrapCrossAlignment.center,
                    spacing: 13,
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.confirmation_number_rounded,
                              size: 16, color: Colors.blueGrey),
                          const SizedBox(width: 4),
                          Text(
                            '#${request.orderId ?? ''}',
                            style: TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w600,
                              color: Colors.blueGrey[700],
                            ),
                          ),
                        ],
                      ),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.calendar_today_rounded,
                              size: 16, color: Colors.blueGrey[400]),
                          const SizedBox(width: 4),
                          Text(
                            DateFormat('dd MMM yyyy')
                                .format(request.orderDate.toDate()),
                            style: TextStyle(
                                fontSize: 13.5,
                                fontWeight: FontWeight.w500,
                                color: Colors.black.withOpacity(0.82)),
                          ),
                        ],
                      ),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.attach_money_rounded,
                            size: 16,
                            color: Colors.green[800],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '\$${request.basePrice.toStringAsFixed(2)}',
                            style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Colors.green[700]),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Actions
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                PremiumActionIcon(
                  icon: Icons.visibility_rounded,
                  color: Colors.blue,
                  onTap: onView,
                  tooltip: 'View Details',
                ),
                PremiumActionIcon(
                  icon: Icons.check_circle_rounded,
                  color: Colors.green,
                  onTap: onApprove,
                  tooltip: 'Approve',
                ),
                PremiumActionIcon(
                  icon: Icons.close_rounded,
                  color: Colors.red,
                  onTap: onDecline,
                  tooltip: 'Decline',
                ),
                PremiumActionIcon(
                  icon: Icons.chat_bubble_outline_rounded,
                  color: Colors.purple,
                  onTap: onChat,
                  tooltip: 'Chat with Buyer',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class PremiumActionIcon extends StatelessWidget {
  final IconData icon;
  final Color color;
  final VoidCallback onTap;
  final String tooltip;
  const PremiumActionIcon({
    Key? key,
    required this.icon,
    required this.color,
    required this.onTap,
    required this.tooltip,
  }) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 3),
        decoration: BoxDecoration(
            color: color.withOpacity(0.11), shape: BoxShape.circle),
        child: IconButton(
          icon: Icon(icon, color: color, size: 26),
          onPressed: onTap,
          splashRadius: 22,
          tooltip: tooltip,
        ),
      ),
    );
  }
}

// Import your OrderModel and ChatPage (and capitalize extension)

class OrdersTableWidget extends StatefulWidget {
  const OrdersTableWidget({super.key});

  @override
  State<OrdersTableWidget> createState() => _OrdersTableWidgetState();
}

class _OrdersTableWidgetState extends State<OrdersTableWidget> {
  String _selectedTab = 'All';
  final TextEditingController _searchController = TextEditingController();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  List<OrderModel> _orders = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadOrders();
  }

  void _loadOrders() {
    final user = _auth.currentUser;
    if (user == null) return;

    _firestore
        .collection('orders')
        .where('publisherId', isEqualTo: user.uid)
        .where('status',
            whereIn: ['Approved', 'In Progress', 'Completed', 'Declined'])
        .orderBy('orderDate', descending: true)
        .snapshots()
        .listen((snapshot) {
          if (mounted) {
            setState(() {
              _orders = snapshot.docs.map((doc) {
                final data = doc.data();
                data['orderId'] = doc.id;
                return OrderModel.fromMap(data);
              }).toList();
              _isLoading = false;
            });
          }
        }, onError: (e) {
          print('Error loading orders: $e');
          if (mounted) setState(() => _isLoading = false);
          ToastHelper.showError('Error loading orders: $e');
        });
  }

  List<OrderModel> get _filteredOrders {
    if (_selectedTab == 'All') return _orders;
    return _orders.where((order) => order.status == _selectedTab).toList();
  }

  List<OrderModel> get _searchedOrders {
    if (_searchController.text.isEmpty) return _filteredOrders;
    final searchTerm = _searchController.text.toLowerCase();
    return _filteredOrders.where((order) {
      return (order.orderId ?? '').toLowerCase().contains(searchTerm) ||
          order.websiteUrl.toLowerCase().contains(searchTerm) ||
          order.postTitle.toLowerCase().contains(searchTerm);
    }).toList();
  }

  Future<void> _updateOrderStatus(String orderId, String newStatus,
      {String? actionBy}) async {
    try {
      final updateData = <String, dynamic>{
        'status': newStatus,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (actionBy != null) {
        updateData['actionBy'] = actionBy;
        updateData['actionTimestamp'] = FieldValue.serverTimestamp();
      }

      if (newStatus == 'Completed') {
        updateData['completionDate'] = FieldValue.serverTimestamp();
      } else if (newStatus == 'In Progress') {
        updateData['inProgressDate'] = FieldValue.serverTimestamp();
      }

      await _firestore.collection('orders').doc(orderId).update(updateData);
      if (!mounted) return;
      ToastHelper.showSuccess('Order marked as $newStatus successfully');
    } catch (e) {
      print('Error updating order: $e');
      if (!mounted) return;
      ToastHelper.showError('Error updating order: $e');
    }
  }

  Future<void> _completeOrderWithLiveUrl(String orderId, String liveUrl) async {
    try {
      final updateData = <String, dynamic>{
        'status': 'Completed',
        'liveUrl': liveUrl,
        'completionDate': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'actionBy': 'publisher',
        'actionTimestamp': FieldValue.serverTimestamp(),
      };

      await _firestore.collection('orders').doc(orderId).update(updateData);

      // Create notification for buyer
      await _createLiveUrlNotification(orderId, liveUrl);

      if (!mounted) return;
    } catch (e) {
      print('Error completing order with live URL: $e');
      if (!mounted) return;
      throw e; // Re-throw to be handled by the dialog
    }
  }

  Future<void> _createLiveUrlNotification(
      String orderId, String liveUrl) async {
    try {
      // Get order details to find buyer
      final orderDoc = await _firestore.collection('orders').doc(orderId).get();
      if (!orderDoc.exists) return;

      final orderData = orderDoc.data()!;
      final buyerId = orderData['buyerId'] as String;

      // Create notification for buyer
      await _firestore
          .collection('users')
          .doc(buyerId)
          .collection('messages')
          .add({
        'message':
            'Your guest post is now live! Check it out at the provided URL.',
        'type': 'order',
        'senderId': 'system',
        'timestamp': FieldValue.serverTimestamp(),
        'read': false,
        'actionUrl': '/orders', // Navigate to orders page
        'orderId': orderId,
        'liveUrl': liveUrl,
      });
    } catch (e) {
      print('Error creating live URL notification: $e');
      // Don't throw here as this is not critical for order completion
    }
  }

  void _showCompleteOrderDialog(String orderId) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => LiveUrlDialog(
        orderId: orderId,
        onSubmit: (liveUrl) async {
          await _completeOrderWithLiveUrl(orderId, liveUrl);
        },
      ),
    );
  }

  void _showOrderDetails(OrderModel order) {
    bool isMarkdownPreview = true;
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusXL)),
        elevation: 0, // Remove default elevation
        backgroundColor:
            Colors.transparent, // Transparent background for custom container
        insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
        child: Container(
          constraints: const BoxConstraints(maxWidth: 800, maxHeight: 700),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusXL),
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Colors.white, Color(0xFFF8FAFC)],
              stops: [0.0, 1.0],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                spreadRadius: 0,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: StatefulBuilder(
            builder: (context, setState) => Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Premium Header with Order ID and Status
                Container(
                  padding: const EdgeInsets.fromLTRB(28, 28, 28, 20),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(AppTheme.borderRadiusXL),
                      topRight: Radius.circular(AppTheme.borderRadiusXL),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Flexible(
                            child: SelectableText(
                              'Order #${order.orderId}',
                              style: GoogleFonts.poppins(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.textPrimary,
                                letterSpacing: -0.3,
                              ),
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            decoration: BoxDecoration(
                              color:
                                  _statusColor(order.status).withOpacity(0.1),
                              borderRadius:
                                  BorderRadius.circular(AppTheme.borderRadiusL),
                              border: Border.all(
                                color:
                                    _statusColor(order.status).withOpacity(0.2),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(_statusIcon(order.status),
                                    size: 18,
                                    color: _statusColor(order.status)),
                                const SizedBox(width: 8),
                                Text(
                                  order.status,
                                  style: GoogleFonts.poppins(
                                    fontWeight: FontWeight.w600,
                                    fontSize: 14,
                                    color: _statusColor(order.status),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Ordered on ${DateFormat('MMMM d, yyyy').format(order.orderDate.toDate())}',
                            style: GoogleFonts.inter(
                              fontSize: 14,
                              color: AppTheme.textSecondary,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          if (order.status != 'Pending' &&
                              order.actionBy != null)
                            Padding(
                              padding: const EdgeInsets.only(top: 4),
                              child: Text(
                                '${order.status} by ${order.actionBy}',
                                style: GoogleFonts.inter(
                                  fontSize: 14,
                                  color: _statusColor(order.status),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Premium divider with gradient
                Container(
                  height: 1,
                  margin: const EdgeInsets.symmetric(horizontal: 28),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.transparent,
                        AppTheme.borderColor,
                        AppTheme.borderColor,
                        Colors.transparent,
                      ],
                      stops: const [0.0, 0.2, 0.8, 1.0],
                    ),
                  ),
                ),

                // Markdown toggle
                Padding(
                  padding: const EdgeInsets.fromLTRB(28, 16, 28, 0),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius:
                          BorderRadius.circular(AppTheme.borderRadiusL),
                      border: Border.all(color: AppTheme.borderColor),
                    ),
                    child: SwitchListTile(
                      title: Text(
                        'View as Markdown',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      value: isMarkdownPreview,
                      activeColor: AppTheme.primaryColor,
                      onChanged: (bool value) {
                        setState(() => isMarkdownPreview = value);
                      },
                    ),
                  ),
                ),

                // Content in a scrollable container with improved styling
                Flexible(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.fromLTRB(28, 20, 28, 0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Website and Content Section
                        _buildSectionTitle('Content Details'),
                        Card(
                          elevation: 0,
                          color: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(AppTheme.borderRadiusL),
                            side: BorderSide(color: AppTheme.borderColor),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(20),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildDetailItem(
                                  'Title:',
                                  order.postTitle,
                                  Icons.title,
                                  AppTheme.secondaryColor,
                                  isMarkdownPreview,
                                ),
                                _buildDetailItem(
                                  'Content:',
                                  order.postContent,
                                  Icons.article,
                                  AppTheme.accentColor,
                                  isMarkdownPreview,
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 20),

                        // Links and Backlink Section
                        _buildSectionTitle('Links & Backlink Information'),
                        Card(
                          elevation: 0,
                          color: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(AppTheme.borderRadiusL),
                            side: BorderSide(color: AppTheme.borderColor),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(20),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (order.links.isNotEmpty)
                                  _buildDetailItem(
                                    'Links:',
                                    order.links.join(', '),
                                    Icons.link,
                                    AppTheme.infoColor,
                                    isMarkdownPreview,
                                  ),
                                _buildDetailItem(
                                  'Backlink Type:',
                                  order.backlinkType,
                                  Icons.link_rounded,
                                  AppTheme.accentColor,
                                  isMarkdownPreview,
                                ),
                                _buildDetailItem(
                                  'Sponsored:',
                                  order.isSponsored ? 'Yes' : 'No',
                                  Icons.verified,
                                  AppTheme.successColor,
                                  isMarkdownPreview,
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 20),

                        // Payment Information Section
                        _buildSectionTitle('Payment Information'),
                        Card(
                          elevation: 0,
                          margin: EdgeInsets.zero,
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(AppTheme.borderRadiusL),
                            side: BorderSide(
                                color: AppTheme.accentColor.withOpacity(0.3)),
                          ),
                          child: Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              borderRadius:
                                  BorderRadius.circular(AppTheme.borderRadiusL),
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  AppTheme.accentColor.withOpacity(0.05),
                                  AppTheme.accentColor.withOpacity(0.02),
                                ],
                              ),
                            ),
                            child: Column(
                              children: [
                                _buildDetailItem(
                                  'Total Price:',
                                  '\$${order.totalPrice.toStringAsFixed(2)}',
                                  Icons.attach_money,
                                  AppTheme.accentColor,
                                  isMarkdownPreview,
                                  valueStyle: GoogleFonts.poppins(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.textPrimary,
                                  ),
                                ),
                                _buildDetailItem(
                                  'Status:',
                                  order.status,
                                  Icons.info_outline,
                                  _statusColor(order.status),
                                  isMarkdownPreview,
                                  valueStyle: GoogleFonts.poppins(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: _statusColor(order.status),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        // Notes Section
                        if (order.notes != null && order.notes!.isNotEmpty) ...[
                          const SizedBox(height: 20),
                          _buildSectionTitle('Additional Notes'),
                          Card(
                            elevation: 0,
                            color: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius:
                                  BorderRadius.circular(AppTheme.borderRadiusL),
                              side: BorderSide(color: AppTheme.borderColor),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(20),
                              child: _buildDetailItem(
                                'Notes:',
                                order.notes ?? 'None',
                                Icons.note,
                                AppTheme.textSecondary,
                                isMarkdownPreview,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),

                // Action buttons with premium styling
                Container(
                  padding: const EdgeInsets.fromLTRB(28, 20, 28, 28),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(AppTheme.borderRadiusXL),
                      bottomRight: Radius.circular(AppTheme.borderRadiusXL),
                    ),
                    color: Colors.white.withOpacity(0.7),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      if (order.status == 'Approved')
                        ElevatedButton.icon(
                          icon:
                              const Icon(Icons.check_circle_outline, size: 18),
                          label: const Text('Mark as Completed'),
                          style: ElevatedButton.styleFrom(
                            foregroundColor: Colors.white,
                            backgroundColor: AppTheme.successColor,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 20, vertical: 12),
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                              borderRadius:
                                  BorderRadius.circular(AppTheme.borderRadiusM),
                            ),
                            textStyle: GoogleFonts.poppins(
                              fontWeight: FontWeight.w500,
                              fontSize: 14,
                            ),
                          ),
                          onPressed: () {
                            Navigator.pop(context);
                            _showCompleteOrderDialog(order.orderId!);
                          },
                        ),
                      const SizedBox(width: 16),
                      ElevatedButton.icon(
                        icon: const Icon(Icons.chat_bubble_outline, size: 18),
                        label: const Text('Contact Buyer'),
                        style: ElevatedButton.styleFrom(
                          foregroundColor: Colors.white,
                          backgroundColor: AppTheme.primaryColor,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 12),
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(AppTheme.borderRadiusM),
                          ),
                          textStyle: GoogleFonts.poppins(
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                        ),
                        onPressed: () {
                          Navigator.pop(context);
                          showDialog(
                            context: context,
                            builder: (_) => ChatDialog(
                              orderId: order.orderId!,
                              buyerId: order.buyerId,
                              publisherId: order.publisherId,
                              orderTitle: order.websiteDomainName,
                            ),
                          );
                        },
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton.icon(
                        icon: const Icon(Icons.close, size: 18),
                        label: const Text('Close'),
                        style: ElevatedButton.styleFrom(
                          foregroundColor: AppTheme.textPrimary,
                          backgroundColor: Colors.grey.shade100,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 12),
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(AppTheme.borderRadiusM),
                          ),
                          textStyle: GoogleFonts.poppins(
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                        ),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Helper method to build section titles
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 20,
            decoration: BoxDecoration(
              color: AppTheme.accentColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimary,
              letterSpacing: -0.2,
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods for status color and icon
  Color _statusColor(String status) {
    switch (status) {
      case 'Completed':
        return Colors.green[700]!;
      case 'Approved':
        return Colors.blue[600]!;
      case 'In Progress':
        return Colors.purple[600]!;
      case 'Declined':
        return Colors.red[400]!;
      case 'Pending':
        return Colors.orange[700]!;
      default:
        return Colors.grey[600]!;
    }
  }

  IconData _statusIcon(String status) {
    switch (status) {
      case 'Completed':
        return Icons.check_circle_rounded;
      case 'Approved':
        return Icons.verified_rounded;
      case 'In Progress':
        return Icons.sync_rounded;
      case 'Declined':
        return Icons.cancel_rounded;
      case 'Pending':
        return Icons.hourglass_empty;
      default:
        return Icons.help_outline_rounded;
    }
  }

  // Enhanced detail item with premium styling
  Widget _buildDetailItem(String label, String value, IconData icon,
      Color color, bool isMarkdownPreview,
      {TextStyle? valueStyle}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(icon, size: 16, color: color),
              ),
              const SizedBox(width: 10),
              Text(
                label,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: color,
                  letterSpacing: 0.1,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
              border: Border.all(
                color: AppTheme.borderColor,
                width: 1,
              ),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: isMarkdownPreview
                      ? MarkdownBody(
                          data: value,
                          selectable: true,
                          styleSheet: MarkdownStyleSheet(
                            p: valueStyle ??
                                GoogleFonts.inter(
                                  fontSize: 14,
                                  color: AppTheme.textPrimary,
                                  height: 1.5,
                                ),
                            h1: GoogleFonts.poppins(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.textPrimary,
                            ),
                            h2: GoogleFonts.poppins(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.textPrimary,
                            ),
                            h3: GoogleFonts.poppins(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.textPrimary,
                            ),
                            blockquote: GoogleFonts.inter(
                              fontSize: 14,
                              color: AppTheme.textSecondary,
                              fontStyle: FontStyle.italic,
                            ),
                            code: GoogleFonts.sourceCodePro(
                              fontSize: 14,
                              color: AppTheme.accentColor,
                              backgroundColor:
                                  AppTheme.accentColor.withOpacity(0.05),
                            ),
                          ),
                        )
                      : Text(
                          value,
                          style: valueStyle ??
                              GoogleFonts.inter(
                                fontSize: 14,
                                color: AppTheme.textPrimary,
                                height: 1.5,
                              ),
                        ),
                ),
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(20),
                    onTap: () {
                      Clipboard.setData(ClipboardData(text: value));
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            '$label copied to clipboard',
                            style: GoogleFonts.inter(),
                          ),
                          backgroundColor: AppTheme.accentColor,
                          behavior: SnackBarBehavior.floating,
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(AppTheme.borderRadiusM),
                          ),
                        ),
                      );
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(4.0),
                      child: Tooltip(
                        message: 'Copy to clipboard',
                        child: Icon(
                          Icons.copy_rounded,
                          size: 18,
                          color: AppTheme.textSecondary.withOpacity(0.7),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTab(String title, bool isSelected) {
    return GestureDetector(
      onTap: () => setState(() => _selectedTab = title),
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        padding: const EdgeInsets.symmetric(horizontal: 17, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.accentColor : Colors.grey.shade200,
          borderRadius: BorderRadius.circular(10),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                      color: Colors.amber.withOpacity(0.25),
                      blurRadius: 4,
                      offset: const Offset(0, 2))
                ]
              : null,
        ),
        child: Text(
          title,
          style: GoogleFonts.poppins(
              // fontSize: 16,
              color: isSelected ? Colors.white : Colors.black87,
              fontWeight: FontWeight.w500),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isWideScreen = MediaQuery.of(context).size.width > 800;

    return Container(
      constraints: const BoxConstraints(minHeight: 480, maxHeight: 700),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(18),
        gradient: LinearGradient(
          colors: [
            Colors.white,
            Colors.white,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
              color: Colors.blueGrey.withOpacity(0.09),
              blurRadius: 18,
              offset: const Offset(0, 8)),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.timeline, color: AppTheme.accentColor, size: 32),
              const SizedBox(width: 8),
              Text(
                'Orders',
                style: TextStyle(
                  fontFamily: 'Space',
                  fontSize: isWideScreen ? 23 : 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                  letterSpacing: 0.3,
                ),
              ),
            ],
          ),
          const SizedBox(height: 14),
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search by ID, website, or title',
              hintStyle: const TextStyle(
                  fontFamily: 'Cairo', fontSize: 14, color: Colors.grey),
              fillColor: Colors.grey[100],
              filled: true,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide.none,
              ),
              prefixIcon: const Icon(Icons.search, color: Colors.grey),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 11),
            ),
            onChanged: (value) {
              setState(() {});
            },
          ),
          const SizedBox(height: 14),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildTab('All', _selectedTab == 'All'),
                _buildTab('Approved', _selectedTab == 'Approved'),
                _buildTab('In Progress', _selectedTab == 'In Progress'),
                _buildTab('Completed', _selectedTab == 'Completed'),
                _buildTab('Declined', _selectedTab == 'Declined'),
              ],
            ),
          ),
          const SizedBox(height: 20),
          if (_isLoading)
            const Expanded(child: Center(child: CircularProgressIndicator()))
          else if (_searchedOrders.isEmpty)
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'No orders found',
                      style: TextStyle(
                          fontFamily: 'Space',
                          fontSize: 18,
                          color: Colors.grey.shade600),
                    ),
                  ],
                ),
              ),
            )
          else
            Expanded(
              child: ListView.separated(
                itemCount: _searchedOrders.length,
                itemBuilder: (context, index) {
                  final order = _searchedOrders[index];
                  return OrderPremiumCard(
                    order: order,
                    onView: () => _showOrderDetails(order),
                    onMarkCompleted: order.status == 'In Progress'
                        ? () => _showCompleteOrderDialog(order.orderId!)
                        : null,
                    onMarkInProgress: order.status == 'Approved'
                        ? () => _updateOrderStatus(
                            order.orderId!, 'In Progress',
                            actionBy: 'publisher')
                        : null,
                    onChat: () => showDialog(
                      context: context,
                      builder: (_) => ChatDialog(
                        orderId: order.orderId!,
                        buyerId: order.buyerId,
                        publisherId: order.publisherId,
                        orderTitle: order.websiteDomainName,
                      ),
                    ),
                  );
                },
                separatorBuilder: (c, i) => const SizedBox(height: 14),
              ),
            ),
        ],
      ),
    );
  }
}

///
/// Premium Card Widget for Each Order
///
class OrderPremiumCard extends StatelessWidget {
  final OrderModel order;
  final VoidCallback onView;
  final VoidCallback? onMarkCompleted;
  final VoidCallback? onMarkInProgress;
  final VoidCallback onChat;

  const OrderPremiumCard({
    Key? key,
    required this.order,
    required this.onView,
    required this.onMarkCompleted,
    this.onMarkInProgress,
    required this.onChat,
  }) : super(key: key);

  Color _statusColor(String status) {
    switch (status) {
      case 'Completed':
        return Colors.green[700]!;
      case 'Approved':
        return Colors.blue[600]!;
      case 'In Progress':
        return Colors.purple[600]!;
      case 'Declined':
        return Colors.red[400]!;
      default:
        return Colors.grey[600]!;
    }
  }

  IconData _statusIcon(String status) {
    switch (status) {
      case 'Completed':
        return Icons.check_circle_rounded;
      case 'Approved':
        return Icons.verified_rounded;
      case 'In Progress':
        return Icons.sync_rounded;
      case 'Declined':
        return Icons.cancel_rounded;
      default:
        return Icons.help_outline_rounded;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      borderRadius: BorderRadius.circular(18),
      elevation: 0,
      // color: const Color.fromARGB(12, 139, 65, 249),
      child: Container(
        decoration: BoxDecoration(
          color: AppTheme.componentBackColor,
          borderRadius: BorderRadius.circular(18),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 19),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Status
            CircleAvatar(
              backgroundColor: _statusColor(order.status).withOpacity(0.16),
              radius: 28,
              child: Icon(
                _statusIcon(order.status),
                size: 35,
                color: _statusColor(order.status),
              ),
            ),
            const SizedBox(width: 19),
            // Main Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    order.websiteUrl,
                    style: const TextStyle(
                        fontFamily: 'Space',
                        fontWeight: FontWeight.bold,
                        fontSize: 17,
                        color: Colors.black87,
                        overflow: TextOverflow.ellipsis),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    order.postTitle,
                    style: TextStyle(
                        fontFamily: 'Cairo',
                        fontWeight: FontWeight.w500,
                        fontSize: 14.5,
                        color: Colors.grey[700]),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 13,
                    crossAxisAlignment: WrapCrossAlignment.center,
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.confirmation_number_rounded,
                              size: 16, color: Colors.blueGrey),
                          const SizedBox(width: 4),
                          Text(
                            '#${order.orderId ?? ''}',
                            style: GoogleFonts.poppins(
                              fontSize: 13,
                              // fontWeight: FontWeight.w600,
                              color: Colors.blueGrey[700],
                            ),
                          ),
                        ],
                      ),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.calendar_today_rounded,
                              size: 16, color: Colors.blueGrey[400]),
                          const SizedBox(width: 4),
                          Text(
                            DateFormat('dd MMM yyyy')
                                .format(order.orderDate.toDate()),
                            style: GoogleFonts.poppins(
                                fontSize: 13,
                                fontWeight: FontWeight.w500,
                                color: Colors.black.withOpacity(0.82)),
                          ),
                        ],
                      ),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            '\$ ${order.basePrice.toStringAsFixed(2)}',
                            style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.green[700]),
                          ),
                        ],
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 4),
                        decoration: BoxDecoration(
                          color: _statusColor(order.status).withOpacity(0.10),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(_statusIcon(order.status),
                                size: 15, color: _statusColor(order.status)),
                            const SizedBox(width: 3),
                            Text(order.status,
                                style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 13.5,
                                    color: _statusColor(order.status))),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Actions
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _ActionIcon(
                  icon: Icons.visibility_rounded,
                  color: Colors.blue,
                  onTap: onView,
                  tooltip: 'View Details',
                ),
                if (order.status == 'Approved' && onMarkInProgress != null)
                  _ActionIcon(
                    icon: Icons.sync_rounded,
                    color: Colors.purple,
                    onTap: onMarkInProgress!,
                    tooltip: 'Mark as In Progress',
                  ),
                if (order.status == 'In Progress' && onMarkCompleted != null)
                  _ActionIcon(
                    icon: Icons.done_all_rounded,
                    color: Colors.green,
                    onTap: onMarkCompleted!,
                    tooltip: 'Mark as Completed',
                  ),
                _ActionIcon(
                  icon: Icons.chat_bubble_outline_rounded,
                  color: AppTheme.accentColor,
                  onTap: onChat,
                  tooltip: 'Chat with Buyer',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _ActionIcon extends StatelessWidget {
  final IconData icon;
  final Color color;
  final VoidCallback onTap;
  final String tooltip;
  const _ActionIcon({
    Key? key,
    required this.icon,
    required this.color,
    required this.onTap,
    required this.tooltip,
  }) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 3),
        decoration: BoxDecoration(
            color: color.withOpacity(0.12), shape: BoxShape.circle),
        child: IconButton(
          icon: Icon(icon, color: color, size: 26),
          onPressed: onTap,
          splashRadius: 22,
          tooltip: tooltip,
        ),
      ),
    );
  }
}

/// Extension example for .capitalize()
extension StringCasingExtension on String {
  String capitalize() =>
      isEmpty ? this : '${this[0].toUpperCase()}${substring(1).toLowerCase()}';
}

extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${substring(1).toLowerCase()}";
  }
}
