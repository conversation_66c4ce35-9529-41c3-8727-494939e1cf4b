import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:guest_posts/core/theme/app_theme.dart';
import 'package:guest_posts/core/models/policy_model.dart';
import 'package:guest_posts/core/services/policy_service.dart';
import 'package:guest_posts/core/widgets/policy_viewer.dart';
import 'package:guest_posts/core/utils/toast_helper.dart';

/// Dialog for accepting multiple policies in a specific context
class PolicyAcceptanceDialog extends StatefulWidget {
  final String context;
  final String title;
  final String description;
  final VoidCallback? onAllAccepted;
  final VoidCallback? onCancelled;

  const PolicyAcceptanceDialog({
    Key? key,
    required this.context,
    required this.title,
    required this.description,
    this.onAllAccepted,
    this.onCancelled,
  }) : super(key: key);

  @override
  State<PolicyAcceptanceDialog> createState() => _PolicyAcceptanceDialogState();
}

class _PolicyAcceptanceDialogState extends State<PolicyAcceptanceDialog> {
  final PolicyService _policyService = PolicyService();
  List<PolicyType> _requiredPolicies = [];
  Map<PolicyType, bool> _acceptanceStates = {};
  bool _isLoading = true;
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _loadRequiredPolicies();
  }

  Future<void> _loadRequiredPolicies() async {
    setState(() => _isLoading = true);

    try {
      final policies = await _policyService.getRequiredPoliciesForContext(widget.context);
      setState(() {
        _requiredPolicies = policies;
        _acceptanceStates = {for (var policy in policies) policy: false};
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      ToastHelper.showError('Failed to load required policies');
    }
  }

  bool get _allPoliciesAccepted => _acceptanceStates.values.every((accepted) => accepted);

  Future<void> _submitAcceptances() async {
    if (!_allPoliciesAccepted) return;

    setState(() => _isSubmitting = true);

    try {
      // Record acceptance for each policy
      for (final policyType in _requiredPolicies) {
        await _policyService.recordPolicyAcceptance(policyType);
      }

      ToastHelper.showSuccess('Policies accepted successfully');
      
      if (widget.onAllAccepted != null) {
        widget.onAllAccepted!();
      }
      
      Navigator.of(context).pop(true);
    } catch (e) {
      ToastHelper.showError('Failed to record policy acceptance');
    } finally {
      setState(() => _isSubmitting = false);
    }
  }

  void _viewPolicy(PolicyType policyType) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PolicyViewer(
          policyType: policyType,
          showBackButton: true,
          showAcceptButton: false,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusXL),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 600, maxHeight: 700),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusXL),
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Colors.white, Color(0xFFF8FAFC)],
            stops: [0.0, 1.0],
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20,
              spreadRadius: 0,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            Flexible(child: _buildContent()),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(28, 28, 28, 20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppTheme.borderRadiusXL),
          topRight: Radius.circular(AppTheme.borderRadiusXL),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusL),
            ),
            child: Icon(
              Icons.policy_rounded,
              size: 24,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.title,
                  style: GoogleFonts.poppins(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
                ),
                Text(
                  widget.description,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    color: AppTheme.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              if (widget.onCancelled != null) {
                widget.onCancelled!();
              }
              Navigator.of(context).pop(false);
            },
            icon: const Icon(Icons.close_rounded),
            style: IconButton.styleFrom(
              backgroundColor: AppTheme.borderColor.withOpacity(0.5),
              foregroundColor: AppTheme.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Padding(
        padding: EdgeInsets.all(40),
        child: Center(child: CircularProgressIndicator()),
      );
    }

    if (_requiredPolicies.isEmpty) {
      return Padding(
        padding: const EdgeInsets.all(28),
        child: Center(
          child: Text(
            'No policies require acceptance for this action.',
            style: GoogleFonts.inter(
              fontSize: 16,
              color: AppTheme.textSecondary,
            ),
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.fromLTRB(28, 0, 28, 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Please review and accept the following policies:',
            style: GoogleFonts.inter(
              fontSize: 16,
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 20),
          Flexible(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: _requiredPolicies.length,
              itemBuilder: (context, index) {
                final policyType = _requiredPolicies[index];
                return _buildPolicyItem(policyType);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPolicyItem(PolicyType policyType) {
    final isAccepted = _acceptanceStates[policyType] ?? false;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.componentBackColor,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusL),
        border: Border.all(
          color: isAccepted ? AppTheme.successColor : AppTheme.borderColor,
          width: isAccepted ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  policyType.displayName,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                  ),
                ),
              ),
              TextButton(
                onPressed: () => _viewPolicy(policyType),
                child: Text(
                  'View Policy',
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Checkbox(
                value: isAccepted,
                onChanged: (value) {
                  setState(() {
                    _acceptanceStates[policyType] = value ?? false;
                  });
                },
                activeColor: AppTheme.successColor,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'I have read and agree to the ${policyType.displayName}',
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    color: AppTheme.textPrimary,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.fromLTRB(28, 20, 28, 28),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: _isSubmitting ? null : () {
                if (widget.onCancelled != null) {
                  widget.onCancelled!();
                }
                Navigator.of(context).pop(false);
              },
              style: OutlinedButton.styleFrom(
                foregroundColor: AppTheme.textSecondary,
                side: BorderSide(color: AppTheme.borderColor),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
                ),
              ),
              child: Text(
                'Cancel',
                style: GoogleFonts.inter(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: (_allPoliciesAccepted && !_isSubmitting) ? _submitAcceptances : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
                ),
              ),
              child: _isSubmitting
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      'Accept All',
                      style: GoogleFonts.inter(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
