import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:guest_posts/core/models/order_model.dart';
import 'package:guest_posts/core/utils/colors.dart';
import 'package:guest_posts/core/utils/toast_helper.dart';
import 'package:guest_posts/features/chat/chat_dialog.dart';
import 'package:intl/intl.dart';
import 'package:flutter/services.dart';
import 'package:guest_posts/core/theme/app_theme.dart';
import 'package:guest_posts/core/widgets/dispute_status_badge.dart';
import 'package:guest_posts/core/widgets/dispute_details_dialog.dart';
import 'package:guest_posts/core/widgets/dispute_initiation_dialog.dart';
import 'package:url_launcher/url_launcher.dart';

class BuyerOrdersPage extends StatefulWidget {
  const BuyerOrdersPage({super.key});

  @override
  State<BuyerOrdersPage> createState() => _BuyerOrdersPageState();
}

class _BuyerOrdersPageState extends State<BuyerOrdersPage> {
  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SingleChildScrollView(
        child: Container(
          padding: EdgeInsets.all(isSmallScreen ? 16.0 : 16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const BuyerOrdersOverviewWidget(),
              const SizedBox(height: 16),
              const BuyerOrdersCardsWidget(),
            ],
          ),
        ),
      ),
    );
  }
}

class BuyerOrdersOverviewWidget extends StatefulWidget {
  const BuyerOrdersOverviewWidget({super.key});

  @override
  State<BuyerOrdersOverviewWidget> createState() =>
      _BuyerOrdersOverviewWidgetState();
}

class _BuyerOrdersOverviewWidgetState extends State<BuyerOrdersOverviewWidget> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  int _pendingCount = 0;
  int _completedCount = 0;
  int _declinedCount = 0;
  int _inProgressCount = 0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadOrderStats();
  }

  Future<void> _loadOrderStats() async {
    try {
      if (mounted) setState(() => _isLoading = true);
      final user = _auth.currentUser;
      if (user == null) return;

      final snapshot = await _firestore
          .collection('orders')
          .where('buyerId', isEqualTo: user.uid)
          .where('orderDate',
              isGreaterThanOrEqualTo: Timestamp.fromDate(
                  DateTime.now().subtract(const Duration(days: 30))))
          .get();

      int pending = 0, completed = 0, declined = 0, inProgress = 0;
      for (var doc in snapshot.docs) {
        final status = doc['status'];
        if (status == 'Pending') pending++;
        if (status == 'Completed') completed++;
        if (status == 'Declined') declined++;
        if (status == 'In Progress') inProgress++;
      }

      if (mounted) {
        setState(() {
          _pendingCount = pending;
          _completedCount = completed;
          _declinedCount = declined;
          _inProgressCount = inProgress;
          _isLoading = false;
        });
      }
    } catch (e) {
      print(e);
      if (mounted) {
        setState(() => _isLoading = false);
        ToastHelper.showError('Error loading stats: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;
    final isMediumScreen = screenWidth >= 600 && screenWidth < 1200;

    return _isLoading
        ? const Center(child: CircularProgressIndicator())
        : GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: isSmallScreen ? 1 : (isMediumScreen ? 2 : 4),
            mainAxisSpacing: 16,
            crossAxisSpacing: 16,
            childAspectRatio: isSmallScreen ? 2.5 : 2.5,
            children: [
              _buildMetricCard(
                title: 'Pending Orders',
                value: _pendingCount.toString(),
                icon: FontAwesomeIcons.clock,
                color: Colors.amber,
                subtitle: 'Awaiting publisher action',
              ),
              _buildMetricCard(
                title: 'In Progress Orders',
                value: _inProgressCount.toString(),
                icon: FontAwesomeIcons.clock,
                color: Colors.blue,
                subtitle: 'All time',
              ),
              _buildMetricCard(
                title: 'Completed Orders',
                value: _completedCount.toString(),
                icon: FontAwesomeIcons.checkCircle,
                color: Colors.green,
                subtitle: 'All time',
              ),
              _buildMetricCard(
                title: 'Declined Orders',
                value: _declinedCount.toString(),
                icon: FontAwesomeIcons.timesCircle,
                color: Colors.red,
                subtitle: 'All time',
              ),
            ],
          );
  }

  Widget _buildMetricCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required String subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Icon(icon, color: AppTheme.accentColor),
          const SizedBox(height: 8),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const Spacer(),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${value}',
                style: GoogleFonts.poppins(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: const Color.fromARGB(255, 31, 21, 48),
                ),
              ),
              const SizedBox(height: 1),
              Text(
                subtitle,
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: Colors.black.withOpacity(0.6),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// --- The modern card-based order widget ---
class BuyerOrdersCardsWidget extends StatefulWidget {
  const BuyerOrdersCardsWidget({super.key});

  @override
  State<BuyerOrdersCardsWidget> createState() => _BuyerOrdersCardsWidgetState();
}

class _BuyerOrdersCardsWidgetState extends State<BuyerOrdersCardsWidget> {
  String _selectedTab = 'All';
  final TextEditingController _searchController = TextEditingController();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  List<OrderModel> _orders = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadOrders();
  }

  Future<void> _loadOrders() async {
    try {
      if (mounted) setState(() => _isLoading = true);
      final user = _auth.currentUser;
      if (user == null) return;

      final snapshot = await _firestore
          .collection('orders')
          .where('buyerId', isEqualTo: user.uid)
          .orderBy('orderDate', descending: true)
          .get();

      if (mounted) {
        setState(() {
          _orders = snapshot.docs
              .map((doc) => OrderModel.fromMap(doc.data()))
              .toList();
          _isLoading = false;
        });
      }
    } catch (e) {
      print(e);
      if (mounted) {
        setState(() => _isLoading = false);
        ToastHelper.showError('Error loading orders: $e');
      }
    }
  }

  Future<void> _cancelOrder(String orderId) async {
    try {
      await _firestore.collection('orders').doc(orderId).update({
        'status': 'Cancelled',
        'updatedAt': FieldValue.serverTimestamp(),
        'actionBy': 'buyer',
        'actionTimestamp': FieldValue.serverTimestamp(),
      });
      await _loadOrders();
      ToastHelper.showSuccess('Order cancelled successfully');
    } catch (e) {
      ToastHelper.showError('Error cancelling order: $e');
    }
  }

  void _showDisputeDetails(OrderModel order) {
    showDialog(
      context: context,
      builder: (context) => DisputeDetailsDialog(
        order: order,
        userRole: 'buyer',
      ),
    );
  }

  void _showDisputeInitiation(OrderModel order) {
    showDialog(
      context: context,
      builder: (context) => DisputeInitiationDialog(
        order: order,
        userRole: 'buyer',
        onDisputeSubmitted: () {
          // Refresh orders list to show updated dispute status
          _loadOrders();
        },
      ),
    );
  }

  List<OrderModel> get _filteredOrders {
    if (_selectedTab == 'All') return _orders;
    return _orders.where((order) => order.status == _selectedTab).toList();
  }

  List<OrderModel> get _searchedOrders {
    if (_searchController.text.isEmpty) return _filteredOrders;
    final searchTerm = _searchController.text.toLowerCase();
    return _filteredOrders.where((order) {
      return (order.orderId ?? '').toLowerCase().contains(searchTerm) ||
          order.websiteUrl.toLowerCase().contains(searchTerm) ||
          order.postTitle.toLowerCase().contains(searchTerm);
    }).toList();
  }

  void _showOrderDetails(OrderModel order) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusXL)),
        elevation: 0, // Remove default elevation
        backgroundColor:
            Colors.transparent, // Transparent background for custom container
        insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
        child: Container(
          constraints: const BoxConstraints(maxWidth: 800, maxHeight: 700),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusXL),
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Colors.white, Color(0xFFF8FAFC)],
              stops: [0.0, 1.0],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                spreadRadius: 0,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Premium Header with Order ID and Status
              Container(
                padding: const EdgeInsets.fromLTRB(28, 28, 28, 20),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(AppTheme.borderRadiusXL),
                    topRight: Radius.circular(AppTheme.borderRadiusXL),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Flexible(
                          child: SelectableText(
                            'Order #${order.orderId}',
                            style: GoogleFonts.poppins(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.textPrimary,
                              letterSpacing: -0.3,
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color:
                                _getStatusColor(order.status).withOpacity(0.1),
                            borderRadius:
                                BorderRadius.circular(AppTheme.borderRadiusL),
                            border: Border.all(
                              color: _getStatusColor(order.status)
                                  .withOpacity(0.2),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(_getStatusIcon(order.status),
                                  size: 18,
                                  color: _getStatusColor(order.status)),
                              const SizedBox(width: 8),
                              Text(
                                order.status,
                                style: GoogleFonts.poppins(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 14,
                                  color: _getStatusColor(order.status),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Ordered on ${DateFormat('MMMM d, yyyy').format(order.orderDate.toDate())}',
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            color: AppTheme.textSecondary,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        if (order.status != 'Pending' && order.actionBy != null)
                          Padding(
                            padding: const EdgeInsets.only(top: 4),
                            child: Text(
                              '${order.status} by ${order.actionBy}',
                              style: GoogleFonts.inter(
                                fontSize: 14,
                                color: _getStatusColor(order.status),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),

              // Premium divider with gradient
              Container(
                height: 1,
                margin: const EdgeInsets.symmetric(horizontal: 28),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.transparent,
                      AppTheme.borderColor,
                      AppTheme.borderColor,
                      Colors.transparent,
                    ],
                    stops: const [0.0, 0.2, 0.8, 1.0],
                  ),
                ),
              ),

              // Content in a scrollable container with improved styling
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.fromLTRB(28, 20, 28, 0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Website and Content Section
                      _buildSectionTitle('Website & Content Details'),
                      Card(
                        elevation: 0,
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(AppTheme.borderRadiusL),
                          side: BorderSide(color: AppTheme.borderColor),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildSelectableDetailItem(
                                  'Website',
                                  order.websiteUrl,
                                  Icons.language,
                                  AppTheme.primaryColor),
                              _buildSelectableDetailItem(
                                  'Title',
                                  order.postTitle,
                                  Icons.title,
                                  AppTheme.secondaryColor),
                              _buildSelectableDetailItem(
                                  'Content',
                                  order.postContent,
                                  Icons.article,
                                  AppTheme.accentColor),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),

                      // Links and Backlink Section
                      _buildSectionTitle('Links & Backlink Information'),
                      Card(
                        elevation: 0,
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(AppTheme.borderRadiusL),
                          side: BorderSide(color: AppTheme.borderColor),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildSelectableDetailItem(
                                  'Links',
                                  order.links.join(', '),
                                  Icons.link,
                                  AppTheme.infoColor),
                              _buildSelectableDetailItem(
                                  'Backlink Type',
                                  order.backlinkType,
                                  Icons.link_rounded,
                                  AppTheme.accentColor),
                              _buildSelectableDetailItem(
                                  'Sponsored',
                                  order.isSponsored ? 'Yes' : 'No',
                                  Icons.verified,
                                  AppTheme.successColor),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),

                      // Payment Information Section
                      _buildSectionTitle('Payment Information'),
                      Card(
                        elevation: 0,
                        margin: EdgeInsets.zero,
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(AppTheme.borderRadiusL),
                          side: BorderSide(
                              color: AppTheme.accentColor.withOpacity(0.3)),
                        ),
                        child: Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            borderRadius:
                                BorderRadius.circular(AppTheme.borderRadiusL),
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                AppTheme.accentColor.withOpacity(0.05),
                                AppTheme.accentColor.withOpacity(0.02),
                              ],
                            ),
                          ),
                          child: Column(
                            children: [
                              _buildSelectableDetailItem(
                                  'Total Price',
                                  '\$${order.totalPrice.toStringAsFixed(2)}',
                                  Icons.attach_money,
                                  AppTheme.accentColor,
                                  valueStyle: GoogleFonts.poppins(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.textPrimary,
                                  )),
                              _buildSelectableDetailItem(
                                  'Payment Status',
                                  order.paymentStatus,
                                  Icons.payment,
                                  order.paymentStatus == 'Paid'
                                      ? AppTheme.successColor
                                      : AppTheme.errorColor,
                                  valueStyle: GoogleFonts.poppins(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: order.paymentStatus == 'Paid'
                                        ? AppTheme.successColor
                                        : AppTheme.errorColor,
                                  )),
                            ],
                          ),
                        ),
                      ),

                      // Live URL Section (only show if order is completed and has live URL)
                      if (order.status == 'Completed' &&
                          order.liveUrl != null &&
                          order.liveUrl!.isNotEmpty) ...[
                        const SizedBox(height: 20),
                        _buildSectionTitle('Live Guest Post'),
                        Card(
                          elevation: 0,
                          color: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(AppTheme.borderRadiusL),
                            side: BorderSide(
                                color: AppTheme.successColor.withOpacity(0.3)),
                          ),
                          child: Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              borderRadius:
                                  BorderRadius.circular(AppTheme.borderRadiusL),
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  AppTheme.successColor.withOpacity(0.05),
                                  AppTheme.successColor.withOpacity(0.02),
                                ],
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: AppTheme.successColor
                                            .withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Icon(
                                        Icons.link_rounded,
                                        size: 20,
                                        color: AppTheme.successColor,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'Your Guest Post is Live!',
                                            style: GoogleFonts.poppins(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600,
                                              color: AppTheme.successColor,
                                            ),
                                          ),
                                          Text(
                                            'Click the link below to view your published article',
                                            style: GoogleFonts.inter(
                                              fontSize: 14,
                                              color: AppTheme.textSecondary,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 16),
                                Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(
                                        AppTheme.borderRadiusM),
                                    border: Border.all(
                                      color: AppTheme.successColor
                                          .withOpacity(0.2),
                                    ),
                                  ),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: SelectableText(
                                          order.liveUrl!,
                                          style: GoogleFonts.inter(
                                            fontSize: 14,
                                            color: AppTheme.primaryColor,
                                            decoration:
                                                TextDecoration.underline,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Material(
                                            color: Colors.transparent,
                                            child: InkWell(
                                              borderRadius:
                                                  BorderRadius.circular(20),
                                              onTap: () {
                                                Clipboard.setData(ClipboardData(
                                                    text: order.liveUrl!));
                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(
                                                  SnackBar(
                                                    content: Text(
                                                      'Live URL copied to clipboard',
                                                      style:
                                                          GoogleFonts.inter(),
                                                    ),
                                                    backgroundColor:
                                                        AppTheme.successColor,
                                                    behavior: SnackBarBehavior
                                                        .floating,
                                                    shape:
                                                        RoundedRectangleBorder(
                                                      borderRadius: BorderRadius
                                                          .circular(AppTheme
                                                              .borderRadiusM),
                                                    ),
                                                  ),
                                                );
                                              },
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.all(8.0),
                                                child: Tooltip(
                                                  message: 'Copy URL',
                                                  child: Icon(
                                                    Icons.copy_rounded,
                                                    size: 18,
                                                    color:
                                                        AppTheme.successColor,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                          Material(
                                            color: Colors.transparent,
                                            child: InkWell(
                                              borderRadius:
                                                  BorderRadius.circular(20),
                                              onTap: () async {
                                                // Open URL in new tab
                                                final uri =
                                                    Uri.parse(order.liveUrl!);
                                                if (await canLaunchUrl(uri)) {
                                                  await launchUrl(uri,
                                                      mode: LaunchMode
                                                          .externalApplication);
                                                } else {
                                                  if (mounted) {
                                                    ScaffoldMessenger.of(
                                                            context)
                                                        .showSnackBar(
                                                      SnackBar(
                                                        content: Text(
                                                            'Could not open URL'),
                                                        backgroundColor:
                                                            AppTheme.errorColor,
                                                      ),
                                                    );
                                                  }
                                                }
                                              },
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.all(8.0),
                                                child: Tooltip(
                                                  message: 'Open in new tab',
                                                  child: Icon(
                                                    Icons.open_in_new_rounded,
                                                    size: 18,
                                                    color:
                                                        AppTheme.successColor,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],

                      // Rejection reason if applicable
                      if (order.rejectionReason != null &&
                          order.rejectionReason!.isNotEmpty)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 20),
                            _buildSectionTitle('Rejection Information'),
                            Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: AppTheme.errorColor.withOpacity(0.05),
                                borderRadius: BorderRadius.circular(
                                    AppTheme.borderRadiusL),
                                border: Border.all(
                                  color: AppTheme.errorColor.withOpacity(0.3),
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Icon(Icons.error_outline,
                                          color: AppTheme.errorColor),
                                      const SizedBox(width: 8),
                                      Text(
                                        'Rejection Reason',
                                        style: GoogleFonts.poppins(
                                          fontWeight: FontWeight.w600,
                                          fontSize: 15,
                                          color: AppTheme.errorColor,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 12),
                                  SelectableText(
                                    order.rejectionReason!,
                                    style: GoogleFonts.inter(
                                      fontSize: 14,
                                      color: AppTheme.textPrimary,
                                      height: 1.5,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),

                      // Copy all details button
                      Align(
                        alignment: Alignment.centerRight,
                        child: Padding(
                          padding: const EdgeInsets.only(top: 20, bottom: 8),
                          child: TextButton.icon(
                            icon: const Icon(Icons.copy, size: 16),
                            label: const Text('Copy All Details'),
                            onPressed: () {
                              final allDetails = '''
Order ID: ${order.orderId}
Status: ${order.status}
Website: ${order.websiteUrl}
Title: ${order.postTitle}
Content: ${order.postContent}
Links: ${order.links.join(', ')}
Backlink Type: ${order.backlinkType}
Sponsored: ${order.isSponsored ? 'Yes' : 'No'}
Total Price: \$${order.totalPrice.toStringAsFixed(2)}
Payment Status: ${order.paymentStatus}
${order.liveUrl != null && order.liveUrl!.isNotEmpty ? 'Live URL: ${order.liveUrl}' : ''}
${order.rejectionReason != null ? 'Rejection Reason: ${order.rejectionReason}' : ''}
''';
                              Clipboard.setData(
                                  ClipboardData(text: allDetails));
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'Order details copied to clipboard',
                                    style: GoogleFonts.inter(),
                                  ),
                                  backgroundColor: AppTheme.accentColor,
                                  behavior: SnackBarBehavior.floating,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(
                                        AppTheme.borderRadiusM),
                                  ),
                                ),
                              );
                            },
                            style: TextButton.styleFrom(
                              foregroundColor: AppTheme.primaryColor,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 8),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Action buttons with premium styling
              Container(
                padding: const EdgeInsets.fromLTRB(28, 20, 28, 28),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(AppTheme.borderRadiusXL),
                    bottomRight: Radius.circular(AppTheme.borderRadiusXL),
                  ),
                  color: Colors.white.withOpacity(0.7),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    if (order.status == 'Pending')
                      OutlinedButton.icon(
                        icon: const Icon(Icons.cancel, size: 18),
                        label: const Text('Cancel Order'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppTheme.errorColor,
                          side: BorderSide(color: AppTheme.errorColor),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(AppTheme.borderRadiusM),
                          ),
                          textStyle: GoogleFonts.poppins(
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                        ),
                        onPressed: () {
                          Navigator.pop(context);
                          _confirmCancelOrder(order.orderId!);
                        },
                      ),
                    const SizedBox(width: 16),
                    ElevatedButton.icon(
                      icon: const Icon(Icons.chat_bubble_outline, size: 18),
                      label: const Text('Contact Publisher'),
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Colors.white,
                        backgroundColor: AppTheme.successColor,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 12),
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(AppTheme.borderRadiusM),
                        ),
                        textStyle: GoogleFonts.poppins(
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                        ),
                      ),
                      onPressed: () {
                        Navigator.pop(context);
                        showDialog(
                          context: context,
                          builder: (context) => ChatDialog(
                            orderId: order.orderId!,
                            buyerId: order.buyerId,
                            publisherId: order.publisherId,
                            orderTitle: order.websiteDomainName,
                          ),
                        );
                      },
                    ),
                    const SizedBox(width: 16),
                    ElevatedButton.icon(
                      icon: const Icon(Icons.close, size: 18),
                      label: const Text('Close'),
                      style: ElevatedButton.styleFrom(
                        foregroundColor: AppTheme.textPrimary,
                        backgroundColor: Colors.grey.shade100,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 12),
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(AppTheme.borderRadiusM),
                        ),
                        textStyle: GoogleFonts.poppins(
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                        ),
                      ),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper methods for the order details dialog
  Color _getStatusColor(String status) {
    switch (status) {
      case 'Completed':
        return Colors.green;
      case 'Approved':
        return Colors.blue;
      case 'Declined':
      case 'Cancelled':
        return Colors.red;
      case 'Pending':
        return Colors.orange;
      case 'In Progress':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'Completed':
        return Icons.check_circle;
      case 'Approved':
        return Icons.thumb_up;
      case 'Declined':
      case 'Cancelled':
        return Icons.cancel;
      case 'Pending':
        return Icons.hourglass_empty;
      case 'In Progress':
        return Icons.sync;
      default:
        return Icons.info;
    }
  }

  // Helper method to build section titles
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 20,
            decoration: BoxDecoration(
              color: AppTheme.accentColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimary,
              letterSpacing: -0.2,
            ),
          ),
        ],
      ),
    );
  }

  // Enhanced detail item with premium styling
  Widget _buildSelectableDetailItem(
      String label, String value, IconData icon, Color color,
      {TextStyle? valueStyle}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(icon, size: 16, color: color),
              ),
              const SizedBox(width: 10),
              Text(
                label,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: color,
                  letterSpacing: 0.1,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
              border: Border.all(
                color: AppTheme.borderColor,
                width: 1,
              ),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: SelectableText(
                    value,
                    style: valueStyle ??
                        GoogleFonts.inter(
                          fontSize: 14,
                          color: AppTheme.textPrimary,
                          height: 1.5,
                        ),
                  ),
                ),
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(20),
                    onTap: () {
                      Clipboard.setData(ClipboardData(text: value));
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            '$label copied to clipboard',
                            style: GoogleFonts.inter(),
                          ),
                          backgroundColor: AppTheme.accentColor,
                          behavior: SnackBarBehavior.floating,
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(AppTheme.borderRadiusM),
                          ),
                        ),
                      );
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(4.0),
                      child: Tooltip(
                        message: 'Copy to clipboard',
                        child: Icon(
                          Icons.copy_rounded,
                          size: 18,
                          color: AppTheme.textSecondary.withOpacity(0.7),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _confirmCancelOrder(String orderId) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        elevation: 0,
        backgroundColor: Colors.transparent,
        child: Container(
          width: 400,
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.warning_rounded,
                  color: Colors.red.shade700,
                  size: 40,
                ),
              ),
              const SizedBox(height: 20),
              const Text(
                'Cancel Order',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              const Text(
                'Are you sure you want to cancel this order? This action cannot be undone.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.black87,
                  fontSize: 15,
                ),
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        side: BorderSide(color: Colors.grey.shade300),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        'No, Keep Order',
                        style: TextStyle(
                          color: Colors.black87,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        _cancelOrder(orderId);
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        'Yes, Cancel',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTab(String title, bool isSelected) {
    return GestureDetector(
      onTap: () => setState(() => _selectedTab = title),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.accentColor : Colors.grey.shade200,
          borderRadius: BorderRadius.circular(10),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                      color: Colors.amber.withOpacity(0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2))
                ]
              : null,
        ),
        child: Text(
          title,
          style: TextStyle(
              fontFamily: 'Cairo',
              color: isSelected ? Colors.white : Colors.black87,
              fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  // ----- CARD UI CODE -----
  Widget _buildOrderCard(OrderModel order, BuildContext context) {
    Color statusColor;
    IconData statusIcon;
    switch (order.status) {
      case 'Completed':
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case 'Approved':
        statusColor = Colors.blue;
        statusIcon = Icons.thumb_up;
        break;
      case 'Declined':
      case 'Cancelled':
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        break;
      case 'Pending':
        statusColor = Colors.orange;
        statusIcon = Icons.hourglass_empty;
        break;
      case 'In Progress':
        statusColor = Colors.orange;
        statusIcon = Icons.sync;
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.info;
        break;
    }

    return Material(
      color: AppTheme.backgroundColor.withOpacity(0.8),
      borderRadius: BorderRadius.circular(16),
      elevation: 0,
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: () => _showOrderDetails(order),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Status
              Container(
                decoration: BoxDecoration(
                  color: statusColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.all(12),
                child: Icon(statusIcon, color: statusColor, size: 32),
              ),
              const SizedBox(width: 16),
              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Order ID + Amount
                    Row(
                      children: [
                        Flexible(
                          child: Text(
                            "Order ID: ${order.orderId ?? 'N/A'}",
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontFamily: 'Space',
                              fontSize: 16,
                              color: Colors.black87,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            "\$${order.totalPrice.toStringAsFixed(2)}",
                            style: TextStyle(
                              fontFamily: 'Cairo',
                              fontWeight: FontWeight.bold,
                              color: Colors.green.shade700,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 6),
                    // Website + Title
                    Text(
                      order.websiteUrl,
                      style: TextStyle(
                          fontFamily: 'Cairo',
                          fontSize: 13,
                          color: Colors.blue.shade800),
                    ),
                    Text(
                      order.postTitle,
                      style: const TextStyle(
                          fontFamily: 'Cairo',
                          fontSize: 15,
                          color: Colors.black87),
                    ),
                    const SizedBox(height: 8),
                    // Status row
                    Row(
                      children: [
                        _buildChip(
                            order.paymentStatus == 'Paid'
                                ? Colors.green
                                : Colors.red,
                            order.paymentStatus),
                        const SizedBox(width: 6),
                        _buildChip(statusColor, order.status),
                        const SizedBox(width: 6),
                        // Dispute badge
                        if (order.isDisputed == true)
                          DisputeStatusBadge(
                            disputeStatus: order.disputeStatus,
                            isDisputed: order.isDisputed ?? false,
                            fontSize: 11,
                          ),
                        if (order.isDisputed == true) const SizedBox(width: 6),
                        const Icon(Icons.calendar_today,
                            size: 16, color: Colors.grey),
                        const SizedBox(width: 3),
                        Text(
                            DateFormat('dd/MM/yyyy')
                                .format(order.orderDate.toDate()),
                            style: TextStyle(
                                fontSize: 12, color: Colors.grey.shade800)),
                      ],
                    ),

                    // Live URL section (only show if order is completed and has live URL)
                    if (order.status == 'Completed' &&
                        order.liveUrl != null &&
                        order.liveUrl!.isNotEmpty) ...[
                      const SizedBox(height: 12),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppTheme.successColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: AppTheme.successColor.withOpacity(0.3),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.link_rounded,
                              size: 16,
                              color: AppTheme.successColor,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Live URL:',
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                      color: AppTheme.successColor,
                                      fontFamily: 'Cairo',
                                    ),
                                  ),
                                  const SizedBox(height: 2),
                                  GestureDetector(
                                    onTap: () async {
                                      final uri = Uri.parse(order.liveUrl!);
                                      if (await canLaunchUrl(uri)) {
                                        await launchUrl(uri,
                                            mode:
                                                LaunchMode.externalApplication);
                                      }
                                    },
                                    child: Text(
                                      order.liveUrl!,
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: AppTheme.primaryColor,
                                        decoration: TextDecoration.underline,
                                        fontFamily: 'Cairo',
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            IconButton(
                              icon: Icon(
                                Icons.open_in_new_rounded,
                                size: 16,
                                color: AppTheme.successColor,
                              ),
                              onPressed: () async {
                                final uri = Uri.parse(order.liveUrl!);
                                if (await canLaunchUrl(uri)) {
                                  await launchUrl(uri,
                                      mode: LaunchMode.externalApplication);
                                }
                              },
                              tooltip: 'Open Live URL',
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(
                                minWidth: 24,
                                minHeight: 24,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              // Actions column
              Column(
                children: [
                  IconButton(
                    icon: const Icon(Icons.text_snippet_outlined,
                        color: AppTheme.accentColor),
                    tooltip: 'View Details',
                    onPressed: () => _showOrderDetails(order),
                  ),
                  if (order.status == 'Pending' ||
                      order.paymentStatus == 'Pending')
                    IconButton(
                      icon: const Icon(Icons.cancel, color: Colors.red),
                      tooltip: 'Cancel Order',
                      onPressed: () => _confirmCancelOrder(order.orderId!),
                    ),
                  if (order.isDisputed == true)
                    IconButton(
                      icon: const Icon(Icons.gavel_rounded,
                          color: AppTheme.errorColor),
                      tooltip: 'View Dispute Details',
                      onPressed: () => _showDisputeDetails(order),
                    ),
                  if (order.status == 'Completed' && order.isDisputed != true)
                    IconButton(
                      icon: const Icon(Icons.report_problem_rounded,
                          color: AppTheme.warningColor),
                      tooltip: 'Report Issue',
                      onPressed: () => _showDisputeInitiation(order),
                    ),
                  IconButton(
                      icon: const Icon(Icons.chat_bubble_outline_rounded,
                          color: Color.fromARGB(255, 20, 175, 0)),
                      tooltip: 'Chat with Publisher',
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder: (context) => ChatDialog(
                            orderId: order.orderId!,
                            buyerId: order.buyerId,
                            publisherId: order.publisherId,
                            orderTitle: order.websiteDomainName,
                          ),
                        );
                      }),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChip(Color color, String label) => Container(
        decoration: BoxDecoration(
          color: color.withOpacity(0.15),
          borderRadius: BorderRadius.circular(6),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
        child: Text(label,
            style: TextStyle(
                color: color,
                fontWeight: FontWeight.w600,
                fontFamily: 'Cairo')),
      );

  @override
  Widget build(BuildContext context) {
    final isWideScreen = MediaQuery.of(context).size.width > 800;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 4)),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'My Orders',
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isWideScreen ? 24 : 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search orders by ID, website, or title',
              hintStyle: const TextStyle(
                  fontFamily: 'Cairo', fontSize: 14, color: Colors.grey),
              fillColor: AppTheme.componentBackColor,
              filled: true,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              prefixIcon: const Icon(Icons.search, color: Colors.grey),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            onChanged: (value) {
              if (mounted) setState(() {});
            },
          ),
          const SizedBox(height: 16),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildTab('All', _selectedTab == 'All'),
                const SizedBox(width: 8),
                _buildTab('Pending', _selectedTab == 'Pending'),
                const SizedBox(width: 8),
                _buildTab('Approved', _selectedTab == 'Approved'),
                const SizedBox(width: 8),
                _buildTab('In Progress', _selectedTab == 'In Progress'),
                const SizedBox(width: 8),
                _buildTab('Completed', _selectedTab == 'Completed'),
                const SizedBox(width: 8),
                _buildTab('Declined', _selectedTab == 'Declined'),
                const SizedBox(width: 8),
                _buildTab('Cancelled', _selectedTab == 'Cancelled'),
              ],
            ),
          ),
          const SizedBox(height: 24),
          if (_isLoading)
            const Center(child: CircularProgressIndicator())
          else if (_searchedOrders.isEmpty)
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.receipt_long,
                      size: 64, color: Colors.grey.shade400),
                  const SizedBox(height: 16),
                  Text(
                    'No orders found',
                    style: TextStyle(
                        fontFamily: 'Space',
                        fontSize: 18,
                        color: Colors.grey.shade600),
                  ),
                ],
              ),
            )
          else
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _searchedOrders.length,
              separatorBuilder: (_, __) => const SizedBox(height: 16),
              itemBuilder: (context, index) {
                final order = _searchedOrders[index];
                return _buildOrderCard(order, context);
              },
            )
        ],
      ),
    );
  }
}
