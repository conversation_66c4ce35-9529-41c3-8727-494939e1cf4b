import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:guest_posts/core/utils/colors.dart';
import 'package:guest_posts/features/landing/landing_models.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:guest_posts/core/utils/toast_helper.dart';
import 'package:guest_posts/core/theme/app_theme.dart';
import 'package:guest_posts/core/models/policy_model.dart';
import 'package:guest_posts/features/policies/policy_page.dart';

class LandingPage extends StatelessWidget {
  const LandingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Column(
              children: const [
                HeroSection(),
                SizedBox(
                  width: 1200,
                  child: Column(
                    children: [
                      StatsSection(),
                      ServicesSection(),
                      TestimonialsSection(),
                      ContactSection(),
                    ],
                  ),
                ),
                FooterSection(),
              ],
            ),
          ),
          const CustomNavBar(),
        ],
      ),
    );
  }
}

class CustomNavBar extends StatelessWidget {
  const CustomNavBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Row(
        children: [
          Row(
            children: [
              Text(
                'Guest',
                style: GoogleFonts.poppins(
                  fontSize: 26,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                  letterSpacing: -0.5,
                ),
              ),
              Text(
                'Post',
                style: GoogleFonts.poppins(
                  fontSize: 26,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.accentColor,
                  letterSpacing: -0.5,
                ),
              ),
            ],
          ),
          const Spacer(),
          if (MediaQuery.of(context).size.width > 768) ...[
            NavItem(text: 'Home', onTap: () {}),
            NavItem(text: 'Services', onTap: () {}),
            NavItem(text: 'Testimonials', onTap: () {}),
            NavItem(text: 'Contact', onTap: () {}),
          ],
          const SizedBox(width: 24),
          CustomButton(
            text: 'Login',
            variant: ButtonVariant.outlined,
            onTap: () {
              context.go('/auth');
            },
          ),
          const SizedBox(width: 12),
          CustomButton(
            text: 'Sign Up',
            onTap: () {
              context.go('/auth/register');
            },
          ),
        ],
      ),
    );
  }
}

class HeroSection extends StatelessWidget {
  const HeroSection({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<DocumentSnapshot>(
      stream: FirebaseFirestore.instance
          .collection('landing')
          .doc('main')
          .collection('hero')
          .doc('content')
          .snapshots(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError || !snapshot.hasData) {
          return _buildDefaultHero(context);
        }

        final data = snapshot.data!.data() as Map<String, dynamic>?;
        final hero = HeroData.fromFirestore(data ?? {});

        return Container(
          height: MediaQuery.of(context).size.height,
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Row(
            children: [
              if (context.isDesktop) const SizedBox(width: 100),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      hero.title,
                      style: GoogleFonts.poppins(
                        fontSize: AppTheme.fontSizeDisplay,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimary,
                        height: 1.1,
                        letterSpacing: -0.5,
                      ),
                    ).animate().fadeIn().slideX(),
                    const SizedBox(height: 24),
                    Text(
                      hero.subtitle,
                      style: GoogleFonts.inter(
                        fontSize: 18,
                        color: AppTheme.textSecondary,
                        height: 1.6,
                        letterSpacing: 0.1,
                      ),
                    ).animate().fadeIn().slideX(),
                    const SizedBox(height: 32),
                    Row(
                      children: [
                        CustomButton(
                          text: 'Get Started',
                          onTap: () {},
                        ),
                        const SizedBox(width: 16),
                        CustomButton(
                          text: 'Learn More',
                          variant: ButtonVariant.outlined,
                          onTap: () {},
                        ),
                      ],
                    ).animate().fadeIn().slideX(),
                  ],
                ),
              ),
              if (context.isDesktop)
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.all(32),
                          child: Image.network(
                            hero.imageUrl,
                            fit: BoxFit.contain,
                            errorBuilder: (context, error, stackTrace) =>
                                const Icon(Icons.broken_image, size: 100),
                          ),
                        ).animate().fadeIn().slideX(),
                      ),
                      const SizedBox(width: 100),
                    ],
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDefaultHero(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height,
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Row(
        children: [
          if (context.isDesktop) const SizedBox(width: 100),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Premium Guest\nPosting Platform',
                  style: GoogleFonts.poppins(
                    fontSize: 48,
                    fontWeight: FontWeight.bold,
                  ),
                ).animate().fadeIn().slideX(),
                const SizedBox(height: 24),
                Text(
                  "Boost your website's authority with high-quality\nguest posts on premium websites.",
                  style: GoogleFonts.inter(
                    fontSize: 18,
                    color: Colors.grey[600],
                  ),
                ).animate().fadeIn().slideX(),
                const SizedBox(height: 32),
                Row(
                  children: [
                    CustomButton(
                      text: 'Get Started',
                      onTap: () {},
                    ),
                    const SizedBox(width: 16),
                    CustomButton(
                      text: 'Learn More',
                      variant: ButtonVariant.outlined,
                      onTap: () {},
                    ),
                  ],
                ).animate().fadeIn().slideX(),
              ],
            ),
          ),
          if (context.isDesktop)
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(32),
                      child: const Icon(Icons.image, size: 100),
                    ).animate().fadeIn().slideX(),
                  ),
                  const SizedBox(width: 100),
                ],
              ),
            ),
        ],
      ),
    );
  }
}

class StatsSection extends StatelessWidget {
  const StatsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return ResponsiveContainer(
      child: StreamBuilder<QuerySnapshot>(
        stream: FirebaseFirestore.instance
            .collection('landing')
            .doc('main')
            .collection('stats')
            .snapshots(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return const Center(child: Text('Error loading stats'));
          }
          if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
            return const Center(child: Text('No stats available'));
          }

          final stats = snapshot.data!.docs
              .map((doc) => Stat.fromFirestore(
                  doc.data() as Map<String, dynamic>, doc.id))
              .toList();

          return ResponsiveGrid(
            children: stats
                .map((stat) => StatCard(
                      number: stat.number,
                      label: stat.label,
                    ))
                .toList(),
          );
        },
      ),
    );
  }
}

class ServicesSection extends StatelessWidget {
  const ServicesSection({super.key});

  @override
  Widget build(BuildContext context) {
    return ResponsiveContainer(
      padding: const EdgeInsets.symmetric(vertical: 64, horizontal: 24),
      child: Column(
        children: [
          const SectionTitle(
            title: 'Our Services',
            subtitle:
                'Choose from our comprehensive range of guest posting services',
          ),
          const SizedBox(height: 48),
          StreamBuilder<QuerySnapshot>(
            stream: FirebaseFirestore.instance
                .collection('landing')
                .doc('main')
                .collection('services')
                .snapshots(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }
              if (snapshot.hasError) {
                return const Center(child: Text('Error loading services'));
              }
              if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
                return const Center(child: Text('No services available'));
              }

              final services = snapshot.data!.docs
                  .map((doc) => Service.fromFirestore(
                      doc.data() as Map<String, dynamic>, doc.id))
                  .toList();

              return ResponsiveGrid(
                children: services
                    .map((service) => ServiceCard(
                          icon: _getIconFromString(service.icon),
                          title: service.title,
                          description: service.description,
                          features: service.features,
                        ))
                    .toList(),
              );
            },
          ),
        ],
      ),
    );
  }

  IconData _getIconFromString(String iconName) {
    switch (iconName) {
      case 'article':
        return Icons.article;
      case 'link':
        return Icons.link;
      case 'analytics':
        return Icons.analytics;
      default:
        return Icons.info;
    }
  }
}

class TestimonialsSection extends StatelessWidget {
  const TestimonialsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return ResponsiveContainer(
      child: Column(
        children: [
          const SectionTitle(
            title: 'What Our Clients Say',
            subtitle: 'Trusted by businesses worldwide',
          ),
          const SizedBox(height: 48),
          StreamBuilder<QuerySnapshot>(
            stream: FirebaseFirestore.instance
                .collection('landing')
                .doc('main')
                .collection('testimonials')
                .snapshots(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }
              if (snapshot.hasError) {
                return const Center(child: Text('Error loading testimonials'));
              }
              if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
                return const Center(child: Text('No testimonials available'));
              }

              final testimonials = snapshot.data!.docs
                  .map((doc) => Testimonial.fromFirestore(
                      doc.data() as Map<String, dynamic>, doc.id))
                  .toList();

              return ResponsiveGrid(
                children: testimonials
                    .map((testimonial) => TestimonialCard(
                          name: testimonial.name,
                          position: testimonial.position,
                          comment: testimonial.comment,
                          rating: testimonial.rating,
                        ))
                    .toList(),
              );
            },
          ),
        ],
      ),
    );
  }
}

class ContactSection extends StatelessWidget {
  const ContactSection({super.key});

  @override
  Widget build(BuildContext context) {
    final nameController = TextEditingController();
    final emailController = TextEditingController();
    final subjectController = TextEditingController();
    final messageController = TextEditingController();

    return ResponsiveContainer(
      child: Column(
        children: [
          const SectionTitle(
            title: 'Get In Touch',
            subtitle: "We'd love to hear from you",
          ),
          const SizedBox(height: 48),
          Container(
            constraints: const BoxConstraints(maxWidth: 600),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: CustomTextField(
                        hint: 'Name',
                        onChanged: (value) {},
                        controller: nameController,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: CustomTextField(
                        hint: 'Email',
                        onChanged: (value) {},
                        controller: emailController,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                CustomTextField(
                  hint: 'Subject',
                  onChanged: (value) {},
                  controller: subjectController,
                ),
                const SizedBox(height: 16),
                CustomTextField(
                  hint: 'Message',
                  maxLines: 5,
                  onChanged: (value) {},
                  controller: messageController,
                ),
                const SizedBox(height: 24),
                CustomButton(
                  text: 'Send Message',
                  onTap: () async {
                    try {
                      await FirebaseFirestore.instance
                          .collection('landing')
                          .doc('main')
                          .collection('contacts')
                          .add({
                        'name': nameController.text,
                        'email': emailController.text,
                        'subject': subjectController.text,
                        'message': messageController.text,
                        'timestamp': FieldValue.serverTimestamp(),
                      });
                      ToastHelper.showSuccess('Message sent successfully');
                      nameController.clear();
                      emailController.clear();
                      subjectController.clear();
                      messageController.clear();
                    } catch (e) {
                      ToastHelper.showError('Error sending message: $e');
                    }
                  },
                  width: double.infinity,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class FooterSection extends StatelessWidget {
  const FooterSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 80, horizontal: 24),
      color: const Color(0xFF0F172A), // Very dark blue/slate
      child: Column(
        children: [
          StreamBuilder<QuerySnapshot>(
            stream: FirebaseFirestore.instance
                .collection('landing')
                .doc('main')
                .collection('footer')
                .snapshots(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }
              if (snapshot.hasError) {
                return const Center(child: Text('Error loading footer data'));
              }
              if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
                return const Center(child: Text('No footer data available'));
              }

              final footerSections = snapshot.data!.docs
                  .map((doc) => FooterSectionData.fromFirestore(
                      doc.data() as Map<String, dynamic>, doc.id))
                  .toList();

              return ResponsiveGrid(
                minChildWidth: 200,
                children: footerSections
                    .map((section) => FooterColumn(
                          title: section.title,
                          items: section.items,
                        ))
                    .toList(),
              );
            },
          ),
          const SizedBox(height: 64),
          Container(
            height: 1,
            color: const Color(0xFF1E293B), // Dark blue/slate
          ),
          const SizedBox(height: 24),
          // Policy links
          Wrap(
            alignment: WrapAlignment.center,
            spacing: 32,
            runSpacing: 16,
            children: [
              _buildPolicyLink(context, PolicyType.privacy),
              _buildPolicyLink(context, PolicyType.terms),
              _buildPolicyLink(context, PolicyType.refund),
              _buildPolicyLink(context, PolicyType.publisherTerms),
            ],
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '© 2023 ',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: const Color(0xFF94A3B8), // Light slate
                ),
              ),
              Text(
                'Guest',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              Text(
                'Post',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.accentColor,
                ),
              ),
              Text(
                '. All rights reserved.',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: const Color(0xFF94A3B8), // Light slate
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPolicyLink(BuildContext context, PolicyType policyType) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => PolicyPage(policyType: policyType),
          ),
        );
      },
      child: Text(
        policyType.displayName,
        style: GoogleFonts.inter(
          fontSize: 14,
          color: const Color(0xFF94A3B8), // Light slate
          decoration: TextDecoration.underline,
        ),
      ),
    );
  }
}

// Widgets

class NavItem extends StatelessWidget {
  final String text;
  final VoidCallback onTap;

  const NavItem({
    super.key,
    required this.text,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(4),
          hoverColor: Colors.transparent,
          splashColor: AppTheme.accentColor.withOpacity(0.1),
          highlightColor: Colors.transparent,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 4),
            child: Text(
              text,
              style: GoogleFonts.inter(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: AppTheme.textSecondary,
                letterSpacing: 0.2,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

enum ButtonVariant { filled, outlined }

class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback onTap;
  final ButtonVariant variant;
  final double? width;

  const CustomButton({
    super.key,
    required this.text,
    required this.onTap,
    this.variant = ButtonVariant.filled,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: Material(
          color: variant == ButtonVariant.filled
              ? AppTheme.accentColor
              : Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: variant == ButtonVariant.outlined
                ? BorderSide(
                    color: AppTheme.accentColor,
                    width: 1.5,
                  )
                : BorderSide.none,
          ),
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(10),
            hoverColor: variant == ButtonVariant.filled
                ? Colors.white.withOpacity(0.1)
                : AppTheme.accentColor.withOpacity(0.05),
            splashColor: variant == ButtonVariant.filled
                ? Colors.white.withOpacity(0.2)
                : AppTheme.accentColor.withOpacity(0.1),
            highlightColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
              child: Text(
                text,
                textAlign: TextAlign.center,
                style: GoogleFonts.inter(
                  fontSize: 15,
                  color: variant == ButtonVariant.filled
                      ? Colors.white
                      : AppTheme.accentColor,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.3,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class StatCard extends StatelessWidget {
  final String number;
  final String label;

  const StatCard({
    super.key,
    required this.number,
    required this.label,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 250,
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.cardShadow.withOpacity(0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
        border: Border.all(
          color: AppTheme.borderColor.withOpacity(0.1),
        ),
      ),
      child: Column(
        children: [
          Text(
            number,
            style: GoogleFonts.poppins(
              fontSize: 42,
              fontWeight: FontWeight.bold,
              color: AppTheme.accentColor,
              height: 1.2,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            label,
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppTheme.textSecondary,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    ).animate().fadeIn().scale();
  }
}

class ServiceCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String description;
  final List<String> features;

  const ServiceCard({
    super.key,
    required this.icon,
    required this.title,
    required this.description,
    required this.features,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 320,
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.cardShadow.withOpacity(0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
        border: Border.all(
          color: AppTheme.borderColor.withOpacity(0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(14),
            decoration: BoxDecoration(
              color: AppTheme.accentColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(14),
            ),
            child: Icon(icon, color: AppTheme.accentColor, size: 28),
          ),
          const SizedBox(height: 28),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimary,
              height: 1.3,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            description,
            style: GoogleFonts.inter(
              fontSize: 16,
              color: AppTheme.textSecondary,
              height: 1.6,
            ),
          ),
          const SizedBox(height: 28),
          ...features.map((feature) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.check_circle_rounded,
                      color: AppTheme.accentColor,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        feature,
                        style: GoogleFonts.inter(
                          fontSize: 15,
                          color: AppTheme.textPrimary,
                          height: 1.5,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    ).animate().fadeIn().scale();
  }
}

class TestimonialCard extends StatelessWidget {
  final String name;
  final String position;
  final String comment;
  final int rating;

  const TestimonialCard({
    super.key,
    required this.name,
    required this.position,
    required this.comment,
    required this.rating,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 320,
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.cardShadow.withOpacity(0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
        border: Border.all(
          color: AppTheme.borderColor.withOpacity(0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 28,
                backgroundColor: AppTheme.accentColor.withOpacity(0.1),
                child: Text(
                  name.isNotEmpty ? name[0].toUpperCase() : '?',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.accentColor,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                    Text(
                      position,
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        color: AppTheme.textSecondary,
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          Text(
            '"$comment"',
            style: GoogleFonts.inter(
              fontSize: 16,
              color: AppTheme.textPrimary,
              height: 1.6,
              fontStyle: FontStyle.italic,
            ),
          ),
          const SizedBox(height: 24),
          Row(
            children: List.generate(
              5,
              (index) => Icon(
                index < rating ? Icons.star_rounded : Icons.star_border_rounded,
                color:
                    index < rating ? AppTheme.accentColor : AppTheme.textLight,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    ).animate().fadeIn().scale();
  }
}

class SectionTitle extends StatelessWidget {
  final String title;
  final String subtitle;

  const SectionTitle({
    super.key,
    required this.title,
    required this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          title,
          style: GoogleFonts.poppins(
            fontSize: AppTheme.fontSizeHuge,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary,
            height: 1.2,
            letterSpacing: -0.3,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        Text(
          subtitle,
          style: GoogleFonts.inter(
            fontSize: 18,
            color: AppTheme.textSecondary,
            height: 1.5,
            letterSpacing: 0.1,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}

class CustomTextField extends StatelessWidget {
  final String hint;
  final int maxLines;
  final ValueChanged<String>? onChanged;
  final TextEditingController? controller;

  const CustomTextField({
    super.key,
    required this.hint,
    this.maxLines = 1,
    this.onChanged,
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return TextField(
      maxLines: maxLines,
      onChanged: onChanged,
      controller: controller,
      style: GoogleFonts.inter(
        fontSize: 16,
        color: AppTheme.textPrimary,
        height: 1.5,
      ),
      decoration: InputDecoration(
        hintText: hint,
        hintStyle: GoogleFonts.inter(
          fontSize: 16,
          color: AppTheme.textLight,
          height: 1.5,
        ),
        filled: true,
        fillColor: AppTheme.componentBackColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppTheme.accentColor, width: 1.5),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
      ),
    );
  }
}

class FooterColumn extends StatelessWidget {
  final String title;
  final List<String> items;

  const FooterColumn({
    super.key,
    required this.title,
    required this.items,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 220,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              letterSpacing: 0.2,
            ),
          ),
          const SizedBox(height: 24),
          ...items.map(
            (item) => Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: MouseRegion(
                cursor: SystemMouseCursors.click,
                child: Text(
                  item,
                  style: GoogleFonts.inter(
                    fontSize: 15,
                    color: const Color(0xFF94A3B8), // Light slate
                    height: 1.5,
                    letterSpacing: 0.1,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Responsive Extensions

extension ScreenSize on BuildContext {
  double get width => MediaQuery.of(this).size.width;
  bool get isMobile => width < 768;
  bool get isTablet => width >= 768 && width < 1200;
  bool get isDesktop => width >= 1200;
}

class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding:
          padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 64),
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 1200),
        child: child,
      ),
    );
  }
}

extension AnimateWidget on Widget {
  Widget animateOnScroll() {
    return VisibilityDetector(
      key: UniqueKey(),
      onVisibilityChanged: (visibilityInfo) {
        if (visibilityInfo.visibleFraction > 0.2) {
          // Trigger animation when 20% visible
        }
      },
      child: this,
    );
  }
}

class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final double spacing;
  final double runSpacing;
  final double minChildWidth;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.spacing = 24,
    this.runSpacing = 24,
    this.minChildWidth = 300,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        final count = (width / minChildWidth).floor().clamp(1, children.length);
        final itemWidth = (width - (spacing * (count - 1))) / count;

        return Wrap(
          spacing: spacing,
          runSpacing: runSpacing,
          alignment: WrapAlignment.center,
          children: children
              .map((child) => SizedBox(
                    width: itemWidth,
                    child: child,
                  ))
              .toList(),
        );
      },
    );
  }
}
